# 🚀 Tutorial Completo: Gemini Fullstack LangGraph Quickstart en Manjaro XFCE

## 📋 Tabla de Contenidos
1. [Requisitos Previos](#requisitos-previos)
2. [Preparación del Sistema](#preparación-del-sistema)
3. [Configuración del Entorno](#configuración-del-entorno)
4. [Instalación del Proyecto](#instalación-del-proyecto)
5. [Configuración de APIs](#configuración-de-apis)
6. [Ejecución en Desarrollo](#ejecución-en-desarrollo)
7. [Configuración para Producción](#configuración-para-producción)
8. [Solución de Problemas](#solución-de-problemas)

---

## 🎯 Requisitos Previos

### Sistema Operativo
- **Manjaro XFCE** (o cualquier distribución basada en Arch)
- **4GB RAM mínimo** (8GB recomendado)
- **5GB espacio libre** en disco
- **Conexión a internet** estable

### Conocimientos Básicos
- Uso básico de la terminal
- Conceptos básicos de Python y JavaScript
- Familiaridad con Git (opcional pero recomendado)

### API Key de Google Gemini
1. Ve a [Google AI Studio](https://aistudio.google.com/)
2. Inicia sesión con tu cuenta de Google
3. Crea un nuevo proyecto o selecciona uno existente
4. Ve a "Get API Key" y genera una nueva clave
5. **Guarda esta clave**, la necesitarás más adelante

---

## 🔧 Preparación del Sistema

### Paso 1: Actualizar el Sistema
```bash
sudo pacman -Syu
```

### Paso 2: Instalar Herramientas Base
```bash
# Instalar Python y herramientas de desarrollo
sudo pacman -S python python-pip python-virtualenv git

# Instalar Node.js y npm
sudo pacman -S nodejs npm

# Instalar Docker (para producción)
sudo pacman -S docker docker-compose

# Habilitar Docker
sudo systemctl enable docker
sudo systemctl start docker

# Agregar usuario al grupo docker
sudo usermod -aG docker $USER
```

### Paso 3: Verificar Instalaciones
```bash
python3 --version    # Debe mostrar Python 3.x
node --version       # Debe mostrar Node.js v18+
npm --version        # Debe mostrar npm 8+
docker --version     # Debe mostrar Docker 20+
git --version        # Debe mostrar Git 2.x
```

**⚠️ Importante**: Después de agregar tu usuario al grupo docker, cierra sesión y vuelve a iniciar, o ejecuta `newgrp docker`.

---

## 🏗️ Configuración del Entorno

### Paso 1: Crear Directorio de Trabajo
```bash
mkdir -p ~/Gemini-All
cd ~/Gemini-All
```

### Paso 2: Crear Entorno Virtual Python
```bash
python3 -m venv Gemini-Total-Web
```

### Paso 3: Crear Script de Activación
Crea el archivo `activate_gemini_env.sh`:
```bash
nano activate_gemini_env.sh
```

Contenido del archivo:
```bash
#!/bin/bash
# Script para activar el entorno virtual Gemini-Total-Web

VENV_PATH="./Gemini-Total-Web"

echo "🚀 Activando entorno virtual Gemini-Total-Web..."

if [ -d "$VENV_PATH" ]; then
    source "$VENV_PATH/bin/activate"
    echo "✅ Entorno virtual activado correctamente"
    echo "📍 Ubicación: $(pwd)/$VENV_PATH"
    echo "🐍 Python: $(python --version)"
    echo "📦 pip: $(pip --version | cut -d' ' -f1-2)"
    echo ""
    echo "Para desactivar el entorno, ejecuta: deactivate"
else
    echo "❌ Error: El entorno virtual '$VENV_PATH' no existe."
    echo "   Ejecuta primero: python3 -m venv $VENV_PATH"
fi
```

Hacer ejecutable:
```bash
chmod +x activate_gemini_env.sh
```

### Paso 4: Activar y Actualizar pip
```bash
source activate_gemini_env.sh
pip install --upgrade pip
```

---

## 📥 Instalación del Proyecto

### Paso 1: Clonar el Repositorio
```bash
git clone https://github.com/langchain-ai/gemini-fullstack-langgraph-quickstart.git
cd gemini-fullstack-langgraph-quickstart
```

### Paso 2: Instalar Dependencias del Backend
```bash
cd backend
source ../../activate_gemini_env.sh
pip install .
```

**Tiempo estimado**: 5-10 minutos
**Paquetes instalados**: ~72 dependencias

### Paso 3: Instalar Dependencias del Frontend
```bash
cd ../frontend
npm install
npm audit fix  # Corregir vulnerabilidades si las hay
```

**Tiempo estimado**: 3-5 minutos
**Paquetes instalados**: ~390 dependencias

### Paso 4: Verificar Instalación del Frontend
```bash
npm run build
```

Si todo está correcto, verás un mensaje como:
```
✓ built in 2.98s
```

---

## 🔑 Configuración de APIs

### Paso 1: Crear Archivo de Variables de Entorno
```bash
cd ../backend
nano .env
```

### Paso 2: Configurar Variables
Contenido del archivo `.env`:
```env
# Configuración de API Keys para Gemini Fullstack LangGraph
GEMINI_API_KEY=tu_api_key_aqui

# Variables adicionales para desarrollo
# LANGSMITH_API_KEY=  # Opcional: para monitoreo con LangSmith
# REDIS_URI=redis://localhost:6379  # Para producción con Docker
# POSTGRES_URI=postgres://postgres:postgres@localhost:5432/postgres  # Para producción con Docker
```

**⚠️ Importante**: Reemplaza `tu_api_key_aqui` con tu API key real de Google Gemini.

### Paso 3: Verificar Conexión con Gemini
Crea un archivo de prueba:
```bash
nano test_connection.py
```

Contenido:
```python
from langchain_google_genai import ChatGoogleGenerativeAI
from dotenv import load_dotenv
import os

load_dotenv()
api_key = os.getenv('GEMINI_API_KEY')

try:
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash",
        google_api_key=api_key,
        temperature=0.1
    )
    response = llm.invoke("Hola, ¿funciona la conexión?")
    print("✅ Conexión exitosa!")
    print(f"Respuesta: {response.content}")
except Exception as e:
    print(f"❌ Error: {e}")
```

Ejecutar prueba:
```bash
source ../../activate_gemini_env.sh
python test_connection.py
```

---

## 🚀 Ejecución en Desarrollo

### Paso 1: Iniciar el Backend
Abre una nueva terminal:
```bash
cd ~/Gemini-All/gemini-fullstack-langgraph-quickstart/backend
source ../../activate_gemini_env.sh
langgraph dev
```

Verás algo como:
```
🚀 API: http://127.0.0.1:2024
🎨 Studio UI: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024
📚 API Docs: http://127.0.0.1:2024/docs
```

### Paso 2: Iniciar el Frontend
Abre otra terminal:
```bash
cd ~/Gemini-All/gemini-fullstack-langgraph-quickstart/frontend
npm run dev
```

Verás:
```
➜  Local:   http://localhost:5173/app/
➜  Network: use --host to expose
```

### Paso 3: Acceder a la Aplicación
Abre tu navegador y ve a:
- **Aplicación principal**: http://localhost:5173/app/
- **API Backend**: http://127.0.0.1:2024
- **Documentación API**: http://127.0.0.1:2024/docs

---

## 🐳 Configuración para Producción

### Paso 1: Preparar Variables de Entorno
```bash
cd ~/Gemini-All/gemini-fullstack-langgraph-quickstart
cp backend/.env .env
```

### Paso 2: Construir Imagen Docker
```bash
# Si tienes permisos de docker:
docker build -t gemini-fullstack-langgraph .

# Si necesitas sudo:
sudo docker build -t gemini-fullstack-langgraph .
```

### Paso 3: Ejecutar con Docker Compose
```bash
# Iniciar servicios
docker-compose up -d

# Verificar estado
docker-compose ps

# Ver logs
docker-compose logs -f
```

### Paso 4: Acceder a la Aplicación en Producción
- **Aplicación**: http://localhost:8123
- **Base de datos**: PostgreSQL en puerto 5433
- **Cache**: Redis interno

---

## 🔧 Solución de Problemas

### Problema: "pip: command not found"
```bash
sudo pacman -S python-pip
```

### Problema: "docker: permission denied"
```bash
sudo usermod -aG docker $USER
newgrp docker
# O usar sudo: sudo docker comando
```

### Problema: "npm vulnerabilities"
```bash
cd frontend
npm audit fix
```

### Problema: "GEMINI_API_KEY not found"
1. Verifica que el archivo `.env` existe en `/backend/`
2. Verifica que la API key es correcta
3. Asegúrate de que no hay espacios extra en el archivo

### Problema: "Port already in use"
```bash
# Encontrar proceso usando el puerto
sudo netstat -tulpn | grep :5173
sudo netstat -tulpn | grep :2024

# Matar proceso si es necesario
sudo kill -9 PID_DEL_PROCESO
```

---

## 🎉 ¡Felicitaciones!

Has instalado exitosamente el Gemini Fullstack LangGraph Quickstart. Ahora puedes:

1. **Desarrollar**: Modificar el código y ver cambios en tiempo real
2. **Experimentar**: Probar diferentes configuraciones de LangGraph
3. **Desplegar**: Usar Docker para producción
4. **Aprender**: Explorar el código y la documentación

### Próximos Pasos
- Explora la [documentación de LangGraph](https://langchain-ai.github.io/langgraph/)
- Revisa los [ejemplos de Gemini](https://ai.google.dev/examples)
- Únete a la [comunidad de LangChain](https://discord.gg/langchain)

---

## 📞 Soporte

Si encuentras problemas:
1. Revisa el archivo `ERRORES_Y_SOLUCIONES_GEMINI_FULLSTACK.md`
2. Consulta los logs: `docker-compose logs` o terminal output
3. Verifica las versiones de las herramientas instaladas
4. Busca en la documentación oficial del proyecto

---

*Tutorial creado para la instalación en Manjaro XFCE - Versión 1.0*
