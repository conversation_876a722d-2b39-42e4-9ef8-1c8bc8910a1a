#!/usr/bin/env python3
"""
Script de prueba para verificar la conexión con la API de Gemini
"""

import os
import sys
from dotenv import load_dotenv

def test_gemini_connection():
    """Prueba la conexión con la API de Gemini"""
    
    print("🔍 Verificando configuración de Gemini...")
    
    # Cargar variables de entorno
    load_dotenv()
    
    # Verificar que la API key esté configurada
    api_key = os.getenv('GEMINI_API_KEY')
    if not api_key:
        print("❌ Error: GEMINI_API_KEY no está configurada en el archivo .env")
        return False
    
    print(f"✅ GEMINI_API_KEY encontrada: {api_key[:10]}...")
    
    try:
        # Importar y probar la conexión con Gemini
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        print("🔗 Probando conexión con la API de Gemini...")
        
        # Crear instancia del modelo
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0.1
        )
        
        # Hacer una consulta simple
        response = llm.invoke("Ho<PERSON>, ¿puedes confirmar que la conexión funciona? Responde brevemente.")
        
        print("✅ Conexión exitosa con Gemini!")
        print(f"📝 Respuesta: {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error al conectar con Gemini: {str(e)}")
        return False

def test_langgraph_imports():
    """Prueba que todas las dependencias de LangGraph estén disponibles"""
    
    print("\n🔍 Verificando dependencias de LangGraph...")
    
    try:
        import langgraph
        print("✅ LangGraph importado correctamente")

        from langgraph.graph import StateGraph
        print("✅ StateGraph importado correctamente")

        from langgraph.prebuilt import ToolNode
        print("✅ ToolNode importado correctamente")

        return True

    except Exception as e:
        print(f"❌ Error al importar LangGraph: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Iniciando pruebas del backend...")
    print("=" * 50)
    
    # Verificar dependencias
    deps_ok = test_langgraph_imports()
    
    # Verificar conexión con Gemini
    gemini_ok = test_gemini_connection()
    
    print("\n" + "=" * 50)
    print("📊 Resumen de pruebas:")
    print(f"   Dependencias LangGraph: {'✅ OK' if deps_ok else '❌ FALLO'}")
    print(f"   Conexión Gemini: {'✅ OK' if gemini_ok else '❌ FALLO'}")
    
    if deps_ok and gemini_ok:
        print("\n🎉 ¡Todas las pruebas pasaron! El backend está listo.")
        sys.exit(0)
    else:
        print("\n⚠️  Algunas pruebas fallaron. Revisa la configuración.")
        sys.exit(1)
