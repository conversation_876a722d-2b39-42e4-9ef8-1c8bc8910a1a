#!/usr/bin/env python3
"""
Prueba directa de la API key de Gemini
"""
import os
import sys

def test_gemini_api():
    print("🔍 Probando API key de Gemini directamente...")
    
    # API key directa
    api_key = "AIzaSyAqTJGcOTaw8hXkWK7GVVbegLieB2FYHlY"
    print(f"API Key: {api_key[:20]}...")
    print(f"Longitud: {len(api_key)} caracteres")
    
    try:
        from langchain_google_genai import ChatGoogleGenerativeAI
        
        print("✅ Importación exitosa de langchain_google_genai")
        
        # Crear instancia del modelo
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash",
            google_api_key=api_key,
            temperature=0.1
        )
        
        print("✅ Modelo creado exitosamente")
        
        # Hacer una prueba simple
        response = llm.invoke("Ho<PERSON>, ¿funciona la conexión?")
        print("✅ ¡ÉXITO! Conexión con Gemini funcionando")
        print(f"Respuesta: {response.content}")
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        print(f"Tipo de error: {type(e).__name__}")
        
        # Información adicional para diagnóstico
        if "API_KEY_INVALID" in str(e):
            print("\n🔧 DIAGNÓSTICO:")
            print("- La API key no es válida o está expirada")
            print("- Verifica que la key sea correcta en Google AI Studio")
            print("- Asegúrate de que la API de Generative AI esté habilitada")
        
        return False
    
    return True

if __name__ == "__main__":
    success = test_gemini_api()
    sys.exit(0 if success else 1)
