(()=>{var t={583:t=>{var e=function(){var t,e,o,n,a,r,i,s,c=[],u=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"],d="1a",l=!1,f="chongo <Landon <PERSON>urt <PERSON>> /\\../\\",h=52,m={32:{offset:0},64:{offset:[0,0,0,0]},128:{offset:[0,0,0,0,0,0,0,0]},256:{offset:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},512:{offset:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]},1024:{offset:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}};for(t=0;t<256;t++)c[t]=(t>>4&15).toString(16)+(15&t).toString(16);function p(t,e){var o,n,a,r=[0],i="";for(n=0;n<t.length;n+=2){for(o=parseInt(t.substr(n,2),16),a=0;a<r.length;a++)o+=r[a]<<8,r[a]=o%e,o=o/e|0;for(;o>0;)r.push(o%e),o=o/e|0}for(n=r.length-1;n>=0;--n)i+="0123456789abcdefghijklmnopqrstuvwxyz"[r[n]];return i}function g(t,e){return{bits:e,value:t,dec:function(){return p(t,10)},hex:function(){return t},str:function(){return p(t,36)}}}function b(t,e){return{bits:e,value:t,dec:function(){return t.toString()},hex:function(){return c[t>>>24]+c[t>>>16&255]+c[t>>>8&255]+c[255&t]},str:function(){return t.toString(36)}}}function C(t,e){return{bits:e,value:t,dec:function(){return t.toString()},hex:function(){return("0000000000000000"+t.toString(16)).substr(-13)},str:function(){return t.toString(36)}}}function y(t,c){var u="object"==typeof t?JSON.stringify(t):t;switch(c||h){case 32:return e(u);case 64:return n(u);case 128:return a(u);case 256:return r(u);case 512:return i(u);case 1024:return s(u);default:return o(u)}}function k(t){if("1a"===t)d=t,e=l?O:v,o=l?S:N,n=l?E:D,a=l?W:I,r=l?J:K,i=l?$:B,s=l?Y:q;else{if("1"!==t)throw new Error("Supported FNV versions: 1, 1a");d=t,e=l?x:w,o=l?j:M,n=l?P:T,a=l?z:U,r=l?R:L,i=l?V:F,s=l?Q:H}}function _(t){t?(l=!0,e="1a"==d?O:x,o="1a"==d?S:j,n="1a"==d?E:P,a="1a"==d?W:z,r="1a"==d?J:R,i="1a"==d?$:V,s="1a"==d?Y:Q):(l=!1,e="1a"==d?v:w,o="1a"==d?N:M,n="1a"==d?D:T,a="1a"==d?I:U,r="1a"==d?K:L,i="1a"==d?B:F,s="1a"==d?q:H)}function A(t){var e,o,n=d;for(var a in(t=t||0===t?t:f)===f&&k("1"),m){for(m[a].offset=[],o=0;o<a/16;o++)m[a].offset[o]=0;for(e=y(t,parseInt(a,10)).hex(),o=0;o<a/16;o++)m[a].offset[o]=parseInt(e.substr(4*o,4),16)}k(n)}function v(t){var e,o=t.length-3,n=m[32].offset,a=0,r=0|n[1],i=0,s=0|n[0];for(e=0;e<o;)i=403*s,i+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=t.charCodeAt(e++))<<8)+((a=403*r)>>>16)&65535,r=65535&a;for(;e<o+3;)i=403*s,s=(i+=(r^=t.charCodeAt(e++))<<8)+((a=403*r)>>>16)&65535,r=65535&a;return b((s<<16>>>0)+r,32)}function w(t){var e,o=t.length-3,n=m[32].offset,a=0,r=0|n[1],i=0,s=0|n[0];for(e=0;e<o;)i=403*s,i+=r<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=t.charCodeAt(e++))<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=t.charCodeAt(e++);for(;e<o+3;)i=403*s,s=(i+=r<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=t.charCodeAt(e++);return b((s<<16>>>0)+r,32)}function O(t){var e,o,n=t.length,a=m[32].offset,r=0,i=0|a[1],s=0,c=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=403*c,c=(s+=(i^=e>>6|192)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=403*c,s+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),s+=(i^=e>>12&63|128)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),c=(s+=(i^=e>>6&63|128)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128):(s=403*c,s+=(i^=e>>12|224)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),c=(s+=(i^=e>>6&63|128)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128),s=403*c,c=(s+=i<<8)+((r=403*i)>>>16)&65535,i=65535&r;return b((c<<16>>>0)+i,32)}function x(t){var e,o,n=t.length,a=m[32].offset,r=0,i=0|a[1],s=0,c=0|a[0];for(o=0;o<n;o++)s=403*c,c=(s+=i<<8)+((r=403*i)>>>16)&65535,i=65535&r,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=403*c,c=(s+=(i^=e>>6|192)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=403*c,s+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),s+=(i^=e>>12&63|128)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),c=(s+=(i^=e>>6&63|128)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128):(s=403*c,s+=(i^=e>>12|224)<<8,i=65535&(r=403*i),s=403*(c=s+(r>>>16)&65535),c=(s+=(i^=e>>6&63|128)<<8)+((r=403*i)>>>16)&65535,i=65535&r,i^=63&e|128);return b((c<<16>>>0)+i,32)}function N(t){var e,o=t.length-3,n=m[64].offset,a=0,r=0|n[3],i=0,s=0|n[2],c=0,u=0|n[1],d=0,l=0|n[0];for(e=0;e<o;)i=435*s,c=435*u,d=435*l,c+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c;for(;e<o+3;)i=435*s,c=435*u,d=435*l,c+=(r^=t.charCodeAt(e++))<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c;return C(281474976710656*(15&l)+4294967296*u+65536*s+(r^l>>4),52)}function M(t){var e,o=t.length-3,n=m[64].offset,a=0,r=0|n[3],i=0,s=0|n[2],c=0,u=0|n[1],d=0,l=0|n[0];for(e=0;e<o;)i=435*s,c=435*u,d=435*l,c+=r<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=t.charCodeAt(e++))<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c,r^=t.charCodeAt(e++);for(;e<o+3;)i=435*s,c=435*u,d=435*l,c+=r<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c,r^=t.charCodeAt(e++);return C(281474976710656*(15&l)+4294967296*u+65536*s+(r^l>>4),52)}function S(t){var e,o,n=t.length,a=m[64].offset,r=0,i=0|a[3],s=0,c=0|a[2],u=0,d=0|a[1],l=0,f=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=435*c,u=435*d,l=435*f,u+=(i^=e>>6|192)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=435*c,u=435*d,l=435*f,u+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>12&63|128)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>6&63|128)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128):(s=435*c,u=435*d,l=435*f,u+=(i^=e>>12|224)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>6&63|128)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128),s=435*c,u=435*d,l=435*f,u+=i<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u;return C(281474976710656*(15&f)+4294967296*d+65536*c+(i^f>>4),52)}function j(t){var e,o,n=t.length,a=m[64].offset,r=0,i=0|a[3],s=0,c=0|a[2],u=0,d=0|a[1],l=0,f=0|a[0];for(o=0;o<n;o++)s=435*c,u=435*d,l=435*f,u+=i<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=435*c,u=435*d,l=435*f,u+=(i^=e>>6|192)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=435*c,u=435*d,l=435*f,u+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>12&63|128)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>6&63|128)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128):(s=435*c,u=435*d,l=435*f,u+=(i^=e>>12|224)<<8,i=65535&(r=435*i),f=(l+=c<<8)+((u+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(c=65535&s),u=435*(d=65535&u),l=435*f,u+=(i^=e>>6&63|128)<<8,l+=c<<8,i=65535&(r=435*i),c=65535&(s+=r>>>16),f=l+((u+=s>>>16)>>>16)&65535,d=65535&u,i^=63&e|128);return C(281474976710656*(15&f)+4294967296*d+65536*c+(i^f>>4),52)}function D(t){var e,o=t.length-3,n=m[64].offset,a=0,r=0|n[3],i=0,s=0|n[2],u=0,d=0|n[1],l=0,f=0|n[0];for(e=0;e<o;)i=435*s,u=435*d,l=435*f,u+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u;for(;e<o+3;)i=435*s,u=435*d,l=435*f,u+=(r^=t.charCodeAt(e++))<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u;return g(c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],64)}function T(t){var e,o=t.length-3,n=m[64].offset,a=0,r=0|n[3],i=0,s=0|n[2],u=0,d=0|n[1],l=0,f=0|n[0];for(e=0;e<o;)i=435*s,u=435*d,l=435*f,u+=r<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=t.charCodeAt(e++))<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u,r^=t.charCodeAt(e++);for(;e<o+3;)i=435*s,u=435*d,l=435*f,u+=r<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u,r^=t.charCodeAt(e++);return g(c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],64)}function E(t){var e,o,n=t.length,a=m[64].offset,r=0,i=0|a[3],s=0,u=0|a[2],d=0,l=0|a[1],f=0,h=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=435*u,d=435*l,f=435*h,d+=(i^=e>>6|192)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=435*u,d=435*l,f=435*h,d+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>12&63|128)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>6&63|128)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128):(s=435*u,d=435*l,f=435*h,d+=(i^=e>>12|224)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>6&63|128)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128),s=435*u,d=435*l,f=435*h,d+=i<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d;return g(c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],64)}function P(t){var e,o,n=t.length,a=m[64].offset,r=0,i=0|a[3],s=0,u=0|a[2],d=0,l=0|a[1],f=0,h=0|a[0];for(o=0;o<n;o++)s=435*u,d=435*l,f=435*h,d+=i<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=435*u,d=435*l,f=435*h,d+=(i^=e>>6|192)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=435*u,d=435*l,f=435*h,d+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>12&63|128)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>6&63|128)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128):(s=435*u,d=435*l,f=435*h,d+=(i^=e>>12|224)<<8,i=65535&(r=435*i),h=(f+=u<<8)+((d+=(s+=r>>>16)>>>16)>>>16)&65535,s=435*(u=65535&s),d=435*(l=65535&d),f=435*h,d+=(i^=e>>6&63|128)<<8,f+=u<<8,i=65535&(r=435*i),u=65535&(s+=r>>>16),h=f+((d+=s>>>16)>>>16)&65535,l=65535&d,i^=63&e|128);return g(c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],64)}function I(t){var e,o=t.length-3,n=m[128].offset,a=0,r=0|n[7],i=0,s=0|n[6],u=0,d=0|n[5],l=0,f=0|n[4],h=0,p=0|n[3],b=0,C=0|n[2],y=0,k=0|n[1],_=0,A=0|n[0];for(e=0;e<o;)i=315*s,u=315*d,l=315*f,h=315*p,b=315*C,y=315*k,_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,_+=d<<8,r=65535&(a=315*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),A=_+((y+=b>>>16)>>>16)&65535,k=65535&y;for(;e<o+3;)i=315*s,u=315*d,l=315*f,h=315*p,b=315*C,y=315*k,_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,_+=d<<8,r=65535&(a=315*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),A=_+((y+=b>>>16)>>>16)&65535,k=65535&y;return g(c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],128)}function U(t){var e,o=t.length-3,n=m[128].offset,a=0,r=0|n[7],i=0,s=0|n[6],u=0,d=0|n[5],l=0,f=0|n[4],h=0,p=0|n[3],b=0,C=0|n[2],y=0,k=0|n[1],_=0,A=0|n[0];for(e=0;e<o;)i=315*s,u=315*d,l=315*f,h=315*p,b=315*C,y=315*k,_=315*A,b+=r<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,r=65535&(a=315*r),A=(_+=d<<8)+((y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=315*(s=65535&i),u=315*(d=65535&u),l=315*(f=65535&l),h=315*(p=65535&h),b=315*(C=65535&b),y=315*(k=65535&y),_=315*A,b+=(r^=t.charCodeAt(e++))<<8,y+=s<<8,_+=d<<8,r=65535&(a=315*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),A=_+((y+=b>>>16)>>>16)&65535,k=65535&y,r^=t.charCodeAt(e++);for(;e<o+3;)i=315*s,u=315*d,l=315*f,h=315*p,b=315*C,y=315*k,_=315*A,b+=r<<8,y+=s<<8,_+=d<<8,r=65535&(a=315*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),A=_+((y+=b>>>16)>>>16)&65535,k=65535&y,r^=t.charCodeAt(e++);return g(c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],128)}function W(t){var e,o,n=t.length,a=m[128].offset,r=0,i=0|a[7],s=0,u=0|a[6],d=0,l=0|a[5],f=0,h=0|a[4],p=0,b=0|a[3],C=0,y=0|a[2],k=0,_=0|a[1],A=0,v=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=e>>6|192)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>12&63|128)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>6&63|128)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128):(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=e>>12|224)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>6&63|128)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128),s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=i<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k;return g(c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],128)}function z(t){var e,o,n=t.length,a=m[128].offset,r=0,i=0|a[7],s=0,u=0|a[6],d=0,l=0|a[5],f=0,h=0|a[4],p=0,b=0|a[3],C=0,y=0|a[2],k=0,_=0|a[1],A=0,v=0|a[0];for(o=0;o<n;o++)s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=i<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=e>>6|192)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>12&63|128)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>6&63|128)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128):(s=315*u,d=315*l,f=315*h,p=315*b,C=315*y,k=315*_,A=315*v,C+=(i^=e>>12|224)<<8,k+=u<<8,i=65535&(r=315*i),v=(A+=l<<8)+((k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=315*(u=65535&s),d=315*(l=65535&d),f=315*(h=65535&f),p=315*(b=65535&p),C=315*(y=65535&C),k=315*(_=65535&k),A=315*v,C+=(i^=e>>6&63|128)<<8,k+=u<<8,A+=l<<8,i=65535&(r=315*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),v=A+((k+=C>>>16)>>>16)&65535,_=65535&k,i^=63&e|128);return g(c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],128)}function K(t){var e,o=t.length-3,n=m[256].offset,a=0,r=0|n[15],i=0,s=0|n[14],u=0,d=0|n[13],l=0,f=0|n[12],h=0,p=0|n[11],b=0,C=0|n[10],y=0,k=0|n[9],_=0,A=0|n[8],v=0,w=0|n[7],O=0,x=0|n[6],N=0,M=0|n[5],S=0,j=0|n[4],D=0,T=0|n[3],E=0,P=0|n[2],I=0,U=0|n[1],W=0,z=0|n[0];for(e=0;e<o;)i=355*s,u=355*d,l=355*f,h=355*p,b=355*C,y=355*k,_=355*A,v=355*w,O=355*x,N=355*M,S=355*j,D=355*T,E=355*P,I=355*U,W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,W+=C<<8,r=65535&(a=355*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),z=W+((I+=E>>>16)>>>16)&65535,U=65535&I;for(;e<o+3;)i=355*s,u=355*d,l=355*f,h=355*p,b=355*C,y=355*k,_=355*A,v=355*w,O=355*x,N=355*M,S=355*j,D=355*T,E=355*P,I=355*U,W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,W+=C<<8,r=65535&(a=355*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),z=W+((I+=E>>>16)>>>16)&65535,U=65535&I;return g(c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],256)}function L(t){var e,o=t.length-3,n=m[256].offset,a=0,r=0|n[15],i=0,s=0|n[14],u=0,d=0|n[13],l=0,f=0|n[12],h=0,p=0|n[11],b=0,C=0|n[10],y=0,k=0|n[9],_=0,A=0|n[8],v=0,w=0|n[7],O=0,x=0|n[6],N=0,M=0|n[5],S=0,j=0|n[4],D=0,T=0|n[3],E=0,P=0|n[2],I=0,U=0|n[1],W=0,z=0|n[0];for(e=0;e<o;)i=355*s,u=355*d,l=355*f,h=355*p,b=355*C,y=355*k,_=355*A,v=355*w,O=355*x,N=355*M,S=355*j,D=355*T,E=355*P,I=355*U,W=355*z,N+=r<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,r=65535&(a=355*r),z=(W+=C<<8)+((I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=355*(s=65535&i),u=355*(d=65535&u),l=355*(f=65535&l),h=355*(p=65535&h),b=355*(C=65535&b),y=355*(k=65535&y),_=355*(A=65535&_),v=355*(w=65535&v),O=355*(x=65535&O),N=355*(M=65535&N),S=355*(j=65535&S),D=355*(T=65535&D),E=355*(P=65535&E),I=355*(U=65535&I),W=355*z,N+=(r^=t.charCodeAt(e++))<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,W+=C<<8,r=65535&(a=355*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),z=W+((I+=E>>>16)>>>16)&65535,U=65535&I,r^=t.charCodeAt(e++);for(;e<o+3;)i=355*s,u=355*d,l=355*f,h=355*p,b=355*C,y=355*k,_=355*A,v=355*w,O=355*x,N=355*M,S=355*j,D=355*T,E=355*P,I=355*U,W=355*z,N+=r<<8,S+=s<<8,D+=d<<8,E+=f<<8,I+=p<<8,W+=C<<8,r=65535&(a=355*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),z=W+((I+=E>>>16)>>>16)&65535,U=65535&I,r^=t.charCodeAt(e++);return g(c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],256)}function J(t){var e,o,n=t.length,a=m[256].offset,r=0,i=0|a[15],s=0,u=0|a[14],d=0,l=0|a[13],f=0,h=0|a[12],p=0,b=0|a[11],C=0,y=0|a[10],k=0,_=0|a[9],A=0,v=0|a[8],w=0,O=0|a[7],x=0,N=0|a[6],M=0,S=0|a[5],j=0,D=0|a[4],T=0,E=0|a[3],P=0,I=0|a[2],U=0,W=0|a[1],z=0,K=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=e>>6|192)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>12&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>6&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128):(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=e>>12|224)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>6&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128),s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=i<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U;return g(c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],256)}function R(t){var e,o,n=t.length,a=m[256].offset,r=0,i=0|a[15],s=0,u=0|a[14],d=0,l=0|a[13],f=0,h=0|a[12],p=0,b=0|a[11],C=0,y=0|a[10],k=0,_=0|a[9],A=0,v=0|a[8],w=0,O=0|a[7],x=0,N=0|a[6],M=0,S=0|a[5],j=0,D=0|a[4],T=0,E=0|a[3],P=0,I=0|a[2],U=0,W=0|a[1],z=0,K=0|a[0];for(o=0;o<n;o++)s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=i<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=e>>6|192)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>12&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>6&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128):(s=355*u,d=355*l,f=355*h,p=355*b,C=355*y,k=355*_,A=355*v,w=355*O,x=355*N,M=355*S,j=355*D,T=355*E,P=355*I,U=355*W,z=355*K,M+=(i^=e>>12|224)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,i=65535&(r=355*i),K=(z+=y<<8)+((U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=355*(u=65535&s),d=355*(l=65535&d),f=355*(h=65535&f),p=355*(b=65535&p),C=355*(y=65535&C),k=355*(_=65535&k),A=355*(v=65535&A),w=355*(O=65535&w),x=355*(N=65535&x),M=355*(S=65535&M),j=355*(D=65535&j),T=355*(E=65535&T),P=355*(I=65535&P),U=355*(W=65535&U),z=355*K,M+=(i^=e>>6&63|128)<<8,j+=u<<8,T+=l<<8,P+=h<<8,U+=b<<8,z+=y<<8,i=65535&(r=355*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),K=z+((U+=P>>>16)>>>16)&65535,W=65535&U,i^=63&e|128);return g(c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],256)}function B(t){var e,o=t.length-3,n=m[512].offset,a=0,r=0|n[31],i=0,s=0|n[30],u=0,d=0|n[29],l=0,f=0|n[28],h=0,p=0|n[27],b=0,C=0|n[26],y=0,k=0|n[25],_=0,A=0|n[24],v=0,w=0|n[23],O=0,x=0|n[22],N=0,M=0|n[21],S=0,j=0|n[20],D=0,T=0|n[19],E=0,P=0|n[18],I=0,U=0|n[17],W=0,z=0|n[16],K=0,L=0|n[15],J=0,R=0|n[14],B=0,F=0|n[13],$=0,V=0|n[12],q=0,H=0|n[11],Y=0,Q=0|n[10],G=0,X=0|n[9],Z=0,tt=0|n[8],et=0,ot=0|n[7],nt=0,at=0|n[6],rt=0,it=0|n[5],st=0,ct=0|n[4],ut=0,dt=0|n[3],lt=0,ft=0|n[2],ht=0,mt=0|n[1],pt=0,gt=0|n[0];for(e=0;e<o;)i=343*s,u=343*d,l=343*f,h=343*p,b=343*C,y=343*k,_=343*A,v=343*w,O=343*x,N=343*M,S=343*j,D=343*T,E=343*P,I=343*U,W=343*z,K=343*L,J=343*R,B=343*F,$=343*V,q=343*H,Y=343*Q,G=343*X,Z=343*tt,et=343*ot,nt=343*at,rt=343*it,st=343*ct,ut=343*dt,lt=343*ft,ht=343*mt,pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,pt+=M<<8,r=65535&(a=343*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),gt=pt+((ht+=lt>>>16)>>>16)&65535,mt=65535&ht;for(;e<o+3;)i=343*s,u=343*d,l=343*f,h=343*p,b=343*C,y=343*k,_=343*A,v=343*w,O=343*x,N=343*M,S=343*j,D=343*T,E=343*P,I=343*U,W=343*z,K=343*L,J=343*R,B=343*F,$=343*V,q=343*H,Y=343*Q,G=343*X,Z=343*tt,et=343*ot,nt=343*at,rt=343*it,st=343*ct,ut=343*dt,lt=343*ft,ht=343*mt,pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,pt+=M<<8,r=65535&(a=343*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),gt=pt+((ht+=lt>>>16)>>>16)&65535,mt=65535&ht;return g(c[gt>>8]+c[255&gt]+c[mt>>8]+c[255&mt]+c[ft>>8]+c[255&ft]+c[dt>>8]+c[255&dt]+c[ct>>8]+c[255&ct]+c[it>>8]+c[255&it]+c[at>>8]+c[255&at]+c[ot>>8]+c[255&ot]+c[tt>>8]+c[255&tt]+c[X>>8]+c[255&X]+c[Q>>8]+c[255&Q]+c[H>>8]+c[255&H]+c[V>>8]+c[255&V]+c[F>>8]+c[255&F]+c[R>>8]+c[255&R]+c[L>>8]+c[255&L]+c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],512)}function F(t){var e,o=t.length-3,n=m[512].offset,a=0,r=0|n[31],i=0,s=0|n[30],u=0,d=0|n[29],l=0,f=0|n[28],h=0,p=0|n[27],b=0,C=0|n[26],y=0,k=0|n[25],_=0,A=0|n[24],v=0,w=0|n[23],O=0,x=0|n[22],N=0,M=0|n[21],S=0,j=0|n[20],D=0,T=0|n[19],E=0,P=0|n[18],I=0,U=0|n[17],W=0,z=0|n[16],K=0,L=0|n[15],J=0,R=0|n[14],B=0,F=0|n[13],$=0,V=0|n[12],q=0,H=0|n[11],Y=0,Q=0|n[10],G=0,X=0|n[9],Z=0,tt=0|n[8],et=0,ot=0|n[7],nt=0,at=0|n[6],rt=0,it=0|n[5],st=0,ct=0|n[4],ut=0,dt=0|n[3],lt=0,ft=0|n[2],ht=0,mt=0|n[1],pt=0,gt=0|n[0];for(e=0;e<o;)i=343*s,u=343*d,l=343*f,h=343*p,b=343*C,y=343*k,_=343*A,v=343*w,O=343*x,N=343*M,S=343*j,D=343*T,E=343*P,I=343*U,W=343*z,K=343*L,J=343*R,B=343*F,$=343*V,q=343*H,Y=343*Q,G=343*X,Z=343*tt,et=343*ot,nt=343*at,rt=343*it,st=343*ct,ut=343*dt,lt=343*ft,ht=343*mt,pt=343*gt,Y+=r<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,r=65535&(a=343*r),gt=(pt+=M<<8)+((ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=343*(s=65535&i),u=343*(d=65535&u),l=343*(f=65535&l),h=343*(p=65535&h),b=343*(C=65535&b),y=343*(k=65535&y),_=343*(A=65535&_),v=343*(w=65535&v),O=343*(x=65535&O),N=343*(M=65535&N),S=343*(j=65535&S),D=343*(T=65535&D),E=343*(P=65535&E),I=343*(U=65535&I),W=343*(z=65535&W),K=343*(L=65535&K),J=343*(R=65535&J),B=343*(F=65535&B),$=343*(V=65535&$),q=343*(H=65535&q),Y=343*(Q=65535&Y),G=343*(X=65535&G),Z=343*(tt=65535&Z),et=343*(ot=65535&et),nt=343*(at=65535&nt),rt=343*(it=65535&rt),st=343*(ct=65535&st),ut=343*(dt=65535&ut),lt=343*(ft=65535&lt),ht=343*(mt=65535&ht),pt=343*gt,Y+=(r^=t.charCodeAt(e++))<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,pt+=M<<8,r=65535&(a=343*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),gt=pt+((ht+=lt>>>16)>>>16)&65535,mt=65535&ht,r^=t.charCodeAt(e++);for(;e<o+3;)i=343*s,u=343*d,l=343*f,h=343*p,b=343*C,y=343*k,_=343*A,v=343*w,O=343*x,N=343*M,S=343*j,D=343*T,E=343*P,I=343*U,W=343*z,K=343*L,J=343*R,B=343*F,$=343*V,q=343*H,Y=343*Q,G=343*X,Z=343*tt,et=343*ot,nt=343*at,rt=343*it,st=343*ct,ut=343*dt,lt=343*ft,ht=343*mt,pt=343*gt,Y+=r<<8,G+=s<<8,Z+=d<<8,et+=f<<8,nt+=p<<8,rt+=C<<8,st+=k<<8,ut+=A<<8,lt+=w<<8,ht+=x<<8,pt+=M<<8,r=65535&(a=343*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),gt=pt+((ht+=lt>>>16)>>>16)&65535,mt=65535&ht,r^=t.charCodeAt(e++);return g(c[gt>>8]+c[255&gt]+c[mt>>8]+c[255&mt]+c[ft>>8]+c[255&ft]+c[dt>>8]+c[255&dt]+c[ct>>8]+c[255&ct]+c[it>>8]+c[255&it]+c[at>>8]+c[255&at]+c[ot>>8]+c[255&ot]+c[tt>>8]+c[255&tt]+c[X>>8]+c[255&X]+c[Q>>8]+c[255&Q]+c[H>>8]+c[255&H]+c[V>>8]+c[255&V]+c[F>>8]+c[255&F]+c[R>>8]+c[255&R]+c[L>>8]+c[255&L]+c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],512)}function $(t){var e,o,n=t.length,a=m[512].offset,r=0,i=0|a[31],s=0,u=0|a[30],d=0,l=0|a[29],f=0,h=0|a[28],p=0,b=0|a[27],C=0,y=0|a[26],k=0,_=0|a[25],A=0,v=0|a[24],w=0,O=0|a[23],x=0,N=0|a[22],M=0,S=0|a[21],j=0,D=0|a[20],T=0,E=0|a[19],P=0,I=0|a[18],U=0,W=0|a[17],z=0,K=0|a[16],L=0,J=0|a[15],R=0,B=0|a[14],F=0,$=0|a[13],V=0,q=0|a[12],H=0,Y=0|a[11],Q=0,G=0|a[10],X=0,Z=0|a[9],tt=0,et=0|a[8],ot=0,nt=0|a[7],at=0,rt=0|a[6],it=0,st=0|a[5],ct=0,ut=0|a[4],dt=0,lt=0|a[3],ft=0,ht=0|a[2],mt=0,pt=0|a[1],gt=0,bt=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=e>>6|192)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>12&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>6&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128):(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=e>>12|224)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>6&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128),s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=i<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt;return g(c[bt>>8]+c[255&bt]+c[pt>>8]+c[255&pt]+c[ht>>8]+c[255&ht]+c[lt>>8]+c[255&lt]+c[ut>>8]+c[255&ut]+c[st>>8]+c[255&st]+c[rt>>8]+c[255&rt]+c[nt>>8]+c[255&nt]+c[et>>8]+c[255&et]+c[Z>>8]+c[255&Z]+c[G>>8]+c[255&G]+c[Y>>8]+c[255&Y]+c[q>>8]+c[255&q]+c[$>>8]+c[255&$]+c[B>>8]+c[255&B]+c[J>>8]+c[255&J]+c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],512)}function V(t){var e,o,n=t.length,a=m[512].offset,r=0,i=0|a[31],s=0,u=0|a[30],d=0,l=0|a[29],f=0,h=0|a[28],p=0,b=0|a[27],C=0,y=0|a[26],k=0,_=0|a[25],A=0,v=0|a[24],w=0,O=0|a[23],x=0,N=0|a[22],M=0,S=0|a[21],j=0,D=0|a[20],T=0,E=0|a[19],P=0,I=0|a[18],U=0,W=0|a[17],z=0,K=0|a[16],L=0,J=0|a[15],R=0,B=0|a[14],F=0,$=0|a[13],V=0,q=0|a[12],H=0,Y=0|a[11],Q=0,G=0|a[10],X=0,Z=0|a[9],tt=0,et=0|a[8],ot=0,nt=0|a[7],at=0,rt=0|a[6],it=0,st=0|a[5],ct=0,ut=0|a[4],dt=0,lt=0|a[3],ft=0,ht=0|a[2],mt=0,pt=0|a[1],gt=0,bt=0|a[0];for(o=0;o<n;o++)s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=i<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=e>>6|192)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>12&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>6&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128):(s=343*u,d=343*l,f=343*h,p=343*b,C=343*y,k=343*_,A=343*v,w=343*O,x=343*N,M=343*S,j=343*D,T=343*E,P=343*I,U=343*W,z=343*K,L=343*J,R=343*B,F=343*$,V=343*q,H=343*Y,Q=343*G,X=343*Z,tt=343*et,ot=343*nt,at=343*rt,it=343*st,ct=343*ut,dt=343*lt,ft=343*ht,mt=343*pt,gt=343*bt,Q+=(i^=e>>12|224)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,i=65535&(r=343*i),bt=(gt+=S<<8)+((mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=343*(u=65535&s),d=343*(l=65535&d),f=343*(h=65535&f),p=343*(b=65535&p),C=343*(y=65535&C),k=343*(_=65535&k),A=343*(v=65535&A),w=343*(O=65535&w),x=343*(N=65535&x),M=343*(S=65535&M),j=343*(D=65535&j),T=343*(E=65535&T),P=343*(I=65535&P),U=343*(W=65535&U),z=343*(K=65535&z),L=343*(J=65535&L),R=343*(B=65535&R),F=343*($=65535&F),V=343*(q=65535&V),H=343*(Y=65535&H),Q=343*(G=65535&Q),X=343*(Z=65535&X),tt=343*(et=65535&tt),ot=343*(nt=65535&ot),at=343*(rt=65535&at),it=343*(st=65535&it),ct=343*(ut=65535&ct),dt=343*(lt=65535&dt),ft=343*(ht=65535&ft),mt=343*(pt=65535&mt),gt=343*bt,Q+=(i^=e>>6&63|128)<<8,X+=u<<8,tt+=l<<8,ot+=h<<8,at+=b<<8,it+=y<<8,ct+=_<<8,dt+=v<<8,ft+=O<<8,mt+=N<<8,gt+=S<<8,i=65535&(r=343*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),bt=gt+((mt+=ft>>>16)>>>16)&65535,pt=65535&mt,i^=63&e|128);return g(c[bt>>8]+c[255&bt]+c[pt>>8]+c[255&pt]+c[ht>>8]+c[255&ht]+c[lt>>8]+c[255&lt]+c[ut>>8]+c[255&ut]+c[st>>8]+c[255&st]+c[rt>>8]+c[255&rt]+c[nt>>8]+c[255&nt]+c[et>>8]+c[255&et]+c[Z>>8]+c[255&Z]+c[G>>8]+c[255&G]+c[Y>>8]+c[255&Y]+c[q>>8]+c[255&q]+c[$>>8]+c[255&$]+c[B>>8]+c[255&B]+c[J>>8]+c[255&J]+c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],512)}function q(t){var e,o=t.length-3,n=m[1024].offset,a=0,r=0|n[63],i=0,s=0|n[62],u=0,d=0|n[61],l=0,f=0|n[60],h=0,p=0|n[59],b=0,C=0|n[58],y=0,k=0|n[57],_=0,A=0|n[56],v=0,w=0|n[55],O=0,x=0|n[54],N=0,M=0|n[53],S=0,j=0|n[52],D=0,T=0|n[51],E=0,P=0|n[50],I=0,U=0|n[49],W=0,z=0|n[48],K=0,L=0|n[47],J=0,R=0|n[46],B=0,F=0|n[45],$=0,V=0|n[44],q=0,H=0|n[43],Y=0,Q=0|n[42],G=0,X=0|n[41],Z=0,tt=0|n[40],et=0,ot=0|n[39],nt=0,at=0|n[38],rt=0,it=0|n[37],st=0,ct=0|n[36],ut=0,dt=0|n[35],lt=0,ft=0|n[34],ht=0,mt=0|n[33],pt=0,gt=0|n[32],bt=0,Ct=0|n[31],yt=0,kt=0|n[30],_t=0,At=0|n[29],vt=0,wt=0|n[28],Ot=0,xt=0|n[27],Nt=0,Mt=0|n[26],St=0,jt=0|n[25],Dt=0,Tt=0|n[24],Et=0,Pt=0|n[23],It=0,Ut=0|n[22],Wt=0,zt=0|n[21],Kt=0,Lt=0|n[20],Jt=0,Rt=0|n[19],Bt=0,Ft=0|n[18],$t=0,Vt=0|n[17],qt=0,Ht=0|n[16],Yt=0,Qt=0|n[15],Gt=0,Xt=0|n[14],Zt=0,te=0|n[13],ee=0,oe=0|n[12],ne=0,ae=0|n[11],re=0,ie=0|n[10],se=0,ce=0|n[9],ue=0,de=0|n[8],le=0,fe=0|n[7],he=0,me=0|n[6],pe=0,ge=0|n[5],be=0,Ce=0|n[4],ye=0,ke=0|n[3],_e=0,Ae=0|n[2],ve=0,we=0|n[1],Oe=0,xe=0|n[0];for(e=0;e<o;)i=397*s,u=397*d,l=397*f,h=397*p,b=397*C,y=397*k,_=397*A,v=397*w,O=397*x,N=397*M,S=397*j,D=397*T,E=397*P,I=397*U,W=397*z,K=397*L,J=397*R,B=397*F,$=397*V,q=397*H,Y=397*Q,G=397*X,Z=397*tt,et=397*ot,nt=397*at,rt=397*it,st=397*ct,ut=397*dt,lt=397*ft,ht=397*mt,pt=397*gt,bt=397*Ct,yt=397*kt,_t=397*At,vt=397*wt,Ot=397*xt,Nt=397*Mt,St=397*jt,Dt=397*Tt,Et=397*Pt,It=397*Ut,Wt=397*zt,Kt=397*Lt,Jt=397*Rt,Bt=397*Ft,$t=397*Vt,qt=397*Ht,Yt=397*Qt,Gt=397*Xt,Zt=397*te,ee=397*oe,ne=397*ae,re=397*ie,se=397*ce,ue=397*de,le=397*fe,he=397*me,pe=397*ge,be=397*Ce,ye=397*ke,_e=397*Ae,ve=397*we,Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,Oe+=Q<<8,r=65535&(a=397*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),mt=65535&(ht+=lt>>>16),gt=65535&(pt+=ht>>>16),Ct=65535&(bt+=pt>>>16),kt=65535&(yt+=bt>>>16),At=65535&(_t+=yt>>>16),wt=65535&(vt+=_t>>>16),xt=65535&(Ot+=vt>>>16),Mt=65535&(Nt+=Ot>>>16),jt=65535&(St+=Nt>>>16),Tt=65535&(Dt+=St>>>16),Pt=65535&(Et+=Dt>>>16),Ut=65535&(It+=Et>>>16),zt=65535&(Wt+=It>>>16),Lt=65535&(Kt+=Wt>>>16),Rt=65535&(Jt+=Kt>>>16),Ft=65535&(Bt+=Jt>>>16),Vt=65535&($t+=Bt>>>16),Ht=65535&(qt+=$t>>>16),Qt=65535&(Yt+=qt>>>16),Xt=65535&(Gt+=Yt>>>16),te=65535&(Zt+=Gt>>>16),oe=65535&(ee+=Zt>>>16),ae=65535&(ne+=ee>>>16),ie=65535&(re+=ne>>>16),ce=65535&(se+=re>>>16),de=65535&(ue+=se>>>16),fe=65535&(le+=ue>>>16),me=65535&(he+=le>>>16),ge=65535&(pe+=he>>>16),Ce=65535&(be+=pe>>>16),ke=65535&(ye+=be>>>16),Ae=65535&(_e+=ye>>>16),xe=Oe+((ve+=_e>>>16)>>>16)&65535,we=65535&ve;for(;e<o+3;)i=397*s,u=397*d,l=397*f,h=397*p,b=397*C,y=397*k,_=397*A,v=397*w,O=397*x,N=397*M,S=397*j,D=397*T,E=397*P,I=397*U,W=397*z,K=397*L,J=397*R,B=397*F,$=397*V,q=397*H,Y=397*Q,G=397*X,Z=397*tt,et=397*ot,nt=397*at,rt=397*it,st=397*ct,ut=397*dt,lt=397*ft,ht=397*mt,pt=397*gt,bt=397*Ct,yt=397*kt,_t=397*At,vt=397*wt,Ot=397*xt,Nt=397*Mt,St=397*jt,Dt=397*Tt,Et=397*Pt,It=397*Ut,Wt=397*zt,Kt=397*Lt,Jt=397*Rt,Bt=397*Ft,$t=397*Vt,qt=397*Ht,Yt=397*Qt,Gt=397*Xt,Zt=397*te,ee=397*oe,ne=397*ae,re=397*ie,se=397*ce,ue=397*de,le=397*fe,he=397*me,pe=397*ge,be=397*Ce,ye=397*ke,_e=397*Ae,ve=397*we,Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,Oe+=Q<<8,r=65535&(a=397*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),mt=65535&(ht+=lt>>>16),gt=65535&(pt+=ht>>>16),Ct=65535&(bt+=pt>>>16),kt=65535&(yt+=bt>>>16),At=65535&(_t+=yt>>>16),wt=65535&(vt+=_t>>>16),xt=65535&(Ot+=vt>>>16),Mt=65535&(Nt+=Ot>>>16),jt=65535&(St+=Nt>>>16),Tt=65535&(Dt+=St>>>16),Pt=65535&(Et+=Dt>>>16),Ut=65535&(It+=Et>>>16),zt=65535&(Wt+=It>>>16),Lt=65535&(Kt+=Wt>>>16),Rt=65535&(Jt+=Kt>>>16),Ft=65535&(Bt+=Jt>>>16),Vt=65535&($t+=Bt>>>16),Ht=65535&(qt+=$t>>>16),Qt=65535&(Yt+=qt>>>16),Xt=65535&(Gt+=Yt>>>16),te=65535&(Zt+=Gt>>>16),oe=65535&(ee+=Zt>>>16),ae=65535&(ne+=ee>>>16),ie=65535&(re+=ne>>>16),ce=65535&(se+=re>>>16),de=65535&(ue+=se>>>16),fe=65535&(le+=ue>>>16),me=65535&(he+=le>>>16),ge=65535&(pe+=he>>>16),Ce=65535&(be+=pe>>>16),ke=65535&(ye+=be>>>16),Ae=65535&(_e+=ye>>>16),xe=Oe+((ve+=_e>>>16)>>>16)&65535,we=65535&ve;return g(c[xe>>8]+c[255&xe]+c[we>>8]+c[255&we]+c[Ae>>8]+c[255&Ae]+c[ke>>8]+c[255&ke]+c[Ce>>8]+c[255&Ce]+c[ge>>8]+c[255&ge]+c[me>>8]+c[255&me]+c[fe>>8]+c[255&fe]+c[de>>8]+c[255&de]+c[ce>>8]+c[255&ce]+c[ie>>8]+c[255&ie]+c[ae>>8]+c[255&ae]+c[oe>>8]+c[255&oe]+c[te>>8]+c[255&te]+c[Xt>>8]+c[255&Xt]+c[Qt>>8]+c[255&Qt]+c[Ht>>8]+c[255&Ht]+c[Vt>>8]+c[255&Vt]+c[Ft>>8]+c[255&Ft]+c[Rt>>8]+c[255&Rt]+c[Lt>>8]+c[255&Lt]+c[zt>>8]+c[255&zt]+c[Ut>>8]+c[255&Ut]+c[Pt>>8]+c[255&Pt]+c[Tt>>8]+c[255&Tt]+c[jt>>8]+c[255&jt]+c[Mt>>8]+c[255&Mt]+c[xt>>8]+c[255&xt]+c[wt>>8]+c[255&wt]+c[At>>8]+c[255&At]+c[kt>>8]+c[255&kt]+c[Ct>>8]+c[255&Ct]+c[gt>>8]+c[255&gt]+c[mt>>8]+c[255&mt]+c[ft>>8]+c[255&ft]+c[dt>>8]+c[255&dt]+c[ct>>8]+c[255&ct]+c[it>>8]+c[255&it]+c[at>>8]+c[255&at]+c[ot>>8]+c[255&ot]+c[tt>>8]+c[255&tt]+c[X>>8]+c[255&X]+c[Q>>8]+c[255&Q]+c[H>>8]+c[255&H]+c[V>>8]+c[255&V]+c[F>>8]+c[255&F]+c[R>>8]+c[255&R]+c[L>>8]+c[255&L]+c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],1024)}function H(t){var e,o=t.length-3,n=m[1024].offset,a=0,r=0|n[63],i=0,s=0|n[62],u=0,d=0|n[61],l=0,f=0|n[60],h=0,p=0|n[59],b=0,C=0|n[58],y=0,k=0|n[57],_=0,A=0|n[56],v=0,w=0|n[55],O=0,x=0|n[54],N=0,M=0|n[53],S=0,j=0|n[52],D=0,T=0|n[51],E=0,P=0|n[50],I=0,U=0|n[49],W=0,z=0|n[48],K=0,L=0|n[47],J=0,R=0|n[46],B=0,F=0|n[45],$=0,V=0|n[44],q=0,H=0|n[43],Y=0,Q=0|n[42],G=0,X=0|n[41],Z=0,tt=0|n[40],et=0,ot=0|n[39],nt=0,at=0|n[38],rt=0,it=0|n[37],st=0,ct=0|n[36],ut=0,dt=0|n[35],lt=0,ft=0|n[34],ht=0,mt=0|n[33],pt=0,gt=0|n[32],bt=0,Ct=0|n[31],yt=0,kt=0|n[30],_t=0,At=0|n[29],vt=0,wt=0|n[28],Ot=0,xt=0|n[27],Nt=0,Mt=0|n[26],St=0,jt=0|n[25],Dt=0,Tt=0|n[24],Et=0,Pt=0|n[23],It=0,Ut=0|n[22],Wt=0,zt=0|n[21],Kt=0,Lt=0|n[20],Jt=0,Rt=0|n[19],Bt=0,Ft=0|n[18],$t=0,Vt=0|n[17],qt=0,Ht=0|n[16],Yt=0,Qt=0|n[15],Gt=0,Xt=0|n[14],Zt=0,te=0|n[13],ee=0,oe=0|n[12],ne=0,ae=0|n[11],re=0,ie=0|n[10],se=0,ce=0|n[9],ue=0,de=0|n[8],le=0,fe=0|n[7],he=0,me=0|n[6],pe=0,ge=0|n[5],be=0,Ce=0|n[4],ye=0,ke=0|n[3],_e=0,Ae=0|n[2],ve=0,we=0|n[1],Oe=0,xe=0|n[0];for(e=0;e<o;)i=397*s,u=397*d,l=397*f,h=397*p,b=397*C,y=397*k,_=397*A,v=397*w,O=397*x,N=397*M,S=397*j,D=397*T,E=397*P,I=397*U,W=397*z,K=397*L,J=397*R,B=397*F,$=397*V,q=397*H,Y=397*Q,G=397*X,Z=397*tt,et=397*ot,nt=397*at,rt=397*it,st=397*ct,ut=397*dt,lt=397*ft,ht=397*mt,pt=397*gt,bt=397*Ct,yt=397*kt,_t=397*At,vt=397*wt,Ot=397*xt,Nt=397*Mt,St=397*jt,Dt=397*Tt,Et=397*Pt,It=397*Ut,Wt=397*zt,Kt=397*Lt,Jt=397*Rt,Bt=397*Ft,$t=397*Vt,qt=397*Ht,Yt=397*Qt,Gt=397*Xt,Zt=397*te,ee=397*oe,ne=397*ae,re=397*ie,se=397*ce,ue=397*de,le=397*fe,he=397*me,pe=397*ge,be=397*Ce,ye=397*ke,_e=397*Ae,ve=397*we,Oe=397*xe,Wt+=r<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,r=65535&(a=397*r),xe=(Oe+=Q<<8)+((ve+=(_e+=(ye+=(be+=(pe+=(he+=(le+=(ue+=(se+=(re+=(ne+=(ee+=(Zt+=(Gt+=(Yt+=(qt+=($t+=(Bt+=(Jt+=(Kt+=(Wt+=(It+=(Et+=(Dt+=(St+=(Nt+=(Ot+=(vt+=(_t+=(yt+=(bt+=(pt+=(ht+=(lt+=(ut+=(st+=(rt+=(nt+=(et+=(Z+=(G+=(Y+=(q+=($+=(B+=(J+=(K+=(W+=(I+=(E+=(D+=(S+=(N+=(O+=(v+=(_+=(y+=(b+=(h+=(l+=(u+=(i+=a>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,i=397*(s=65535&i),u=397*(d=65535&u),l=397*(f=65535&l),h=397*(p=65535&h),b=397*(C=65535&b),y=397*(k=65535&y),_=397*(A=65535&_),v=397*(w=65535&v),O=397*(x=65535&O),N=397*(M=65535&N),S=397*(j=65535&S),D=397*(T=65535&D),E=397*(P=65535&E),I=397*(U=65535&I),W=397*(z=65535&W),K=397*(L=65535&K),J=397*(R=65535&J),B=397*(F=65535&B),$=397*(V=65535&$),q=397*(H=65535&q),Y=397*(Q=65535&Y),G=397*(X=65535&G),Z=397*(tt=65535&Z),et=397*(ot=65535&et),nt=397*(at=65535&nt),rt=397*(it=65535&rt),st=397*(ct=65535&st),ut=397*(dt=65535&ut),lt=397*(ft=65535&lt),ht=397*(mt=65535&ht),pt=397*(gt=65535&pt),bt=397*(Ct=65535&bt),yt=397*(kt=65535&yt),_t=397*(At=65535&_t),vt=397*(wt=65535&vt),Ot=397*(xt=65535&Ot),Nt=397*(Mt=65535&Nt),St=397*(jt=65535&St),Dt=397*(Tt=65535&Dt),Et=397*(Pt=65535&Et),It=397*(Ut=65535&It),Wt=397*(zt=65535&Wt),Kt=397*(Lt=65535&Kt),Jt=397*(Rt=65535&Jt),Bt=397*(Ft=65535&Bt),$t=397*(Vt=65535&$t),qt=397*(Ht=65535&qt),Yt=397*(Qt=65535&Yt),Gt=397*(Xt=65535&Gt),Zt=397*(te=65535&Zt),ee=397*(oe=65535&ee),ne=397*(ae=65535&ne),re=397*(ie=65535&re),se=397*(ce=65535&se),ue=397*(de=65535&ue),le=397*(fe=65535&le),he=397*(me=65535&he),pe=397*(ge=65535&pe),be=397*(Ce=65535&be),ye=397*(ke=65535&ye),_e=397*(Ae=65535&_e),ve=397*(we=65535&ve),Oe=397*xe,Wt+=(r^=t.charCodeAt(e++))<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,Oe+=Q<<8,r=65535&(a=397*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),mt=65535&(ht+=lt>>>16),gt=65535&(pt+=ht>>>16),Ct=65535&(bt+=pt>>>16),kt=65535&(yt+=bt>>>16),At=65535&(_t+=yt>>>16),wt=65535&(vt+=_t>>>16),xt=65535&(Ot+=vt>>>16),Mt=65535&(Nt+=Ot>>>16),jt=65535&(St+=Nt>>>16),Tt=65535&(Dt+=St>>>16),Pt=65535&(Et+=Dt>>>16),Ut=65535&(It+=Et>>>16),zt=65535&(Wt+=It>>>16),Lt=65535&(Kt+=Wt>>>16),Rt=65535&(Jt+=Kt>>>16),Ft=65535&(Bt+=Jt>>>16),Vt=65535&($t+=Bt>>>16),Ht=65535&(qt+=$t>>>16),Qt=65535&(Yt+=qt>>>16),Xt=65535&(Gt+=Yt>>>16),te=65535&(Zt+=Gt>>>16),oe=65535&(ee+=Zt>>>16),ae=65535&(ne+=ee>>>16),ie=65535&(re+=ne>>>16),ce=65535&(se+=re>>>16),de=65535&(ue+=se>>>16),fe=65535&(le+=ue>>>16),me=65535&(he+=le>>>16),ge=65535&(pe+=he>>>16),Ce=65535&(be+=pe>>>16),ke=65535&(ye+=be>>>16),Ae=65535&(_e+=ye>>>16),xe=Oe+((ve+=_e>>>16)>>>16)&65535,we=65535&ve,r^=t.charCodeAt(e++);for(;e<o+3;)i=397*s,u=397*d,l=397*f,h=397*p,b=397*C,y=397*k,_=397*A,v=397*w,O=397*x,N=397*M,S=397*j,D=397*T,E=397*P,I=397*U,W=397*z,K=397*L,J=397*R,B=397*F,$=397*V,q=397*H,Y=397*Q,G=397*X,Z=397*tt,et=397*ot,nt=397*at,rt=397*it,st=397*ct,ut=397*dt,lt=397*ft,ht=397*mt,pt=397*gt,bt=397*Ct,yt=397*kt,_t=397*At,vt=397*wt,Ot=397*xt,Nt=397*Mt,St=397*jt,Dt=397*Tt,Et=397*Pt,It=397*Ut,Wt=397*zt,Kt=397*Lt,Jt=397*Rt,Bt=397*Ft,$t=397*Vt,qt=397*Ht,Yt=397*Qt,Gt=397*Xt,Zt=397*te,ee=397*oe,ne=397*ae,re=397*ie,se=397*ce,ue=397*de,le=397*fe,he=397*me,pe=397*ge,be=397*Ce,ye=397*ke,_e=397*Ae,ve=397*we,Oe=397*xe,Wt+=r<<8,Kt+=s<<8,Jt+=d<<8,Bt+=f<<8,$t+=p<<8,qt+=C<<8,Yt+=k<<8,Gt+=A<<8,Zt+=w<<8,ee+=x<<8,ne+=M<<8,re+=j<<8,se+=T<<8,ue+=P<<8,le+=U<<8,he+=z<<8,pe+=L<<8,be+=R<<8,ye+=F<<8,_e+=V<<8,ve+=H<<8,Oe+=Q<<8,r=65535&(a=397*r),s=65535&(i+=a>>>16),d=65535&(u+=i>>>16),f=65535&(l+=u>>>16),p=65535&(h+=l>>>16),C=65535&(b+=h>>>16),k=65535&(y+=b>>>16),A=65535&(_+=y>>>16),w=65535&(v+=_>>>16),x=65535&(O+=v>>>16),M=65535&(N+=O>>>16),j=65535&(S+=N>>>16),T=65535&(D+=S>>>16),P=65535&(E+=D>>>16),U=65535&(I+=E>>>16),z=65535&(W+=I>>>16),L=65535&(K+=W>>>16),R=65535&(J+=K>>>16),F=65535&(B+=J>>>16),V=65535&($+=B>>>16),H=65535&(q+=$>>>16),Q=65535&(Y+=q>>>16),X=65535&(G+=Y>>>16),tt=65535&(Z+=G>>>16),ot=65535&(et+=Z>>>16),at=65535&(nt+=et>>>16),it=65535&(rt+=nt>>>16),ct=65535&(st+=rt>>>16),dt=65535&(ut+=st>>>16),ft=65535&(lt+=ut>>>16),mt=65535&(ht+=lt>>>16),gt=65535&(pt+=ht>>>16),Ct=65535&(bt+=pt>>>16),kt=65535&(yt+=bt>>>16),At=65535&(_t+=yt>>>16),wt=65535&(vt+=_t>>>16),xt=65535&(Ot+=vt>>>16),Mt=65535&(Nt+=Ot>>>16),jt=65535&(St+=Nt>>>16),Tt=65535&(Dt+=St>>>16),Pt=65535&(Et+=Dt>>>16),Ut=65535&(It+=Et>>>16),zt=65535&(Wt+=It>>>16),Lt=65535&(Kt+=Wt>>>16),Rt=65535&(Jt+=Kt>>>16),Ft=65535&(Bt+=Jt>>>16),Vt=65535&($t+=Bt>>>16),Ht=65535&(qt+=$t>>>16),Qt=65535&(Yt+=qt>>>16),Xt=65535&(Gt+=Yt>>>16),te=65535&(Zt+=Gt>>>16),oe=65535&(ee+=Zt>>>16),ae=65535&(ne+=ee>>>16),ie=65535&(re+=ne>>>16),ce=65535&(se+=re>>>16),de=65535&(ue+=se>>>16),fe=65535&(le+=ue>>>16),me=65535&(he+=le>>>16),ge=65535&(pe+=he>>>16),Ce=65535&(be+=pe>>>16),ke=65535&(ye+=be>>>16),Ae=65535&(_e+=ye>>>16),xe=Oe+((ve+=_e>>>16)>>>16)&65535,we=65535&ve,r^=t.charCodeAt(e++);return g(c[xe>>8]+c[255&xe]+c[we>>8]+c[255&we]+c[Ae>>8]+c[255&Ae]+c[ke>>8]+c[255&ke]+c[Ce>>8]+c[255&Ce]+c[ge>>8]+c[255&ge]+c[me>>8]+c[255&me]+c[fe>>8]+c[255&fe]+c[de>>8]+c[255&de]+c[ce>>8]+c[255&ce]+c[ie>>8]+c[255&ie]+c[ae>>8]+c[255&ae]+c[oe>>8]+c[255&oe]+c[te>>8]+c[255&te]+c[Xt>>8]+c[255&Xt]+c[Qt>>8]+c[255&Qt]+c[Ht>>8]+c[255&Ht]+c[Vt>>8]+c[255&Vt]+c[Ft>>8]+c[255&Ft]+c[Rt>>8]+c[255&Rt]+c[Lt>>8]+c[255&Lt]+c[zt>>8]+c[255&zt]+c[Ut>>8]+c[255&Ut]+c[Pt>>8]+c[255&Pt]+c[Tt>>8]+c[255&Tt]+c[jt>>8]+c[255&jt]+c[Mt>>8]+c[255&Mt]+c[xt>>8]+c[255&xt]+c[wt>>8]+c[255&wt]+c[At>>8]+c[255&At]+c[kt>>8]+c[255&kt]+c[Ct>>8]+c[255&Ct]+c[gt>>8]+c[255&gt]+c[mt>>8]+c[255&mt]+c[ft>>8]+c[255&ft]+c[dt>>8]+c[255&dt]+c[ct>>8]+c[255&ct]+c[it>>8]+c[255&it]+c[at>>8]+c[255&at]+c[ot>>8]+c[255&ot]+c[tt>>8]+c[255&tt]+c[X>>8]+c[255&X]+c[Q>>8]+c[255&Q]+c[H>>8]+c[255&H]+c[V>>8]+c[255&V]+c[F>>8]+c[255&F]+c[R>>8]+c[255&R]+c[L>>8]+c[255&L]+c[z>>8]+c[255&z]+c[U>>8]+c[255&U]+c[P>>8]+c[255&P]+c[T>>8]+c[255&T]+c[j>>8]+c[255&j]+c[M>>8]+c[255&M]+c[x>>8]+c[255&x]+c[w>>8]+c[255&w]+c[A>>8]+c[255&A]+c[k>>8]+c[255&k]+c[C>>8]+c[255&C]+c[p>>8]+c[255&p]+c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r],1024)}function Y(t){var e,o,n=t.length,a=m[1024].offset,r=0,i=0|a[63],s=0,u=0|a[62],d=0,l=0|a[61],f=0,h=0|a[60],p=0,b=0|a[59],C=0,y=0|a[58],k=0,_=0|a[57],A=0,v=0|a[56],w=0,O=0|a[55],x=0,N=0|a[54],M=0,S=0|a[53],j=0,D=0|a[52],T=0,E=0|a[51],P=0,I=0|a[50],U=0,W=0|a[49],z=0,K=0|a[48],L=0,J=0|a[47],R=0,B=0|a[46],F=0,$=0|a[45],V=0,q=0|a[44],H=0,Y=0|a[43],Q=0,G=0|a[42],X=0,Z=0|a[41],tt=0,et=0|a[40],ot=0,nt=0|a[39],at=0,rt=0|a[38],it=0,st=0|a[37],ct=0,ut=0|a[36],dt=0,lt=0|a[35],ft=0,ht=0|a[34],mt=0,pt=0|a[33],gt=0,bt=0|a[32],Ct=0,yt=0|a[31],kt=0,_t=0|a[30],At=0,vt=0|a[29],wt=0,Ot=0|a[28],xt=0,Nt=0|a[27],Mt=0,St=0|a[26],jt=0,Dt=0|a[25],Tt=0,Et=0|a[24],Pt=0,It=0|a[23],Ut=0,Wt=0|a[22],zt=0,Kt=0|a[21],Lt=0,Jt=0|a[20],Rt=0,Bt=0|a[19],Ft=0,$t=0|a[18],Vt=0,qt=0|a[17],Ht=0,Yt=0|a[16],Qt=0,Gt=0|a[15],Xt=0,Zt=0|a[14],te=0,ee=0|a[13],oe=0,ne=0|a[12],ae=0,re=0|a[11],ie=0,se=0|a[10],ce=0,ue=0|a[9],de=0,le=0|a[8],fe=0,he=0|a[7],me=0,pe=0|a[6],ge=0,be=0|a[5],Ce=0,ye=0|a[4],ke=0,_e=0|a[3],Ae=0,ve=0|a[2],we=0,Oe=0|a[1],xe=0,Ne=0|a[0];for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=e>>6|192)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>12&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>6&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128):(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=e>>12|224)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>6&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128),s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=i<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we;return g(c[Ne>>8]+c[255&Ne]+c[Oe>>8]+c[255&Oe]+c[ve>>8]+c[255&ve]+c[_e>>8]+c[255&_e]+c[ye>>8]+c[255&ye]+c[be>>8]+c[255&be]+c[pe>>8]+c[255&pe]+c[he>>8]+c[255&he]+c[le>>8]+c[255&le]+c[ue>>8]+c[255&ue]+c[se>>8]+c[255&se]+c[re>>8]+c[255&re]+c[ne>>8]+c[255&ne]+c[ee>>8]+c[255&ee]+c[Zt>>8]+c[255&Zt]+c[Gt>>8]+c[255&Gt]+c[Yt>>8]+c[255&Yt]+c[qt>>8]+c[255&qt]+c[$t>>8]+c[255&$t]+c[Bt>>8]+c[255&Bt]+c[Jt>>8]+c[255&Jt]+c[Kt>>8]+c[255&Kt]+c[Wt>>8]+c[255&Wt]+c[It>>8]+c[255&It]+c[Et>>8]+c[255&Et]+c[Dt>>8]+c[255&Dt]+c[St>>8]+c[255&St]+c[Nt>>8]+c[255&Nt]+c[Ot>>8]+c[255&Ot]+c[vt>>8]+c[255&vt]+c[_t>>8]+c[255&_t]+c[yt>>8]+c[255&yt]+c[bt>>8]+c[255&bt]+c[pt>>8]+c[255&pt]+c[ht>>8]+c[255&ht]+c[lt>>8]+c[255&lt]+c[ut>>8]+c[255&ut]+c[st>>8]+c[255&st]+c[rt>>8]+c[255&rt]+c[nt>>8]+c[255&nt]+c[et>>8]+c[255&et]+c[Z>>8]+c[255&Z]+c[G>>8]+c[255&G]+c[Y>>8]+c[255&Y]+c[q>>8]+c[255&q]+c[$>>8]+c[255&$]+c[B>>8]+c[255&B]+c[J>>8]+c[255&J]+c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],1024)}function Q(t){var e,o,n=t.length,a=m[1024].offset,r=0,i=0|a[63],s=0,u=0|a[62],d=0,l=0|a[61],f=0,h=0|a[60],p=0,b=0|a[59],C=0,y=0|a[58],k=0,_=0|a[57],A=0,v=0|a[56],w=0,O=0|a[55],x=0,N=0|a[54],M=0,S=0|a[53],j=0,D=0|a[52],T=0,E=0|a[51],P=0,I=0|a[50],U=0,W=0|a[49],z=0,K=0|a[48],L=0,J=0|a[47],R=0,B=0|a[46],F=0,$=0|a[45],V=0,q=0|a[44],H=0,Y=0|a[43],Q=0,G=0|a[42],X=0,Z=0|a[41],tt=0,et=0|a[40],ot=0,nt=0|a[39],at=0,rt=0|a[38],it=0,st=0|a[37],ct=0,ut=0|a[36],dt=0,lt=0|a[35],ft=0,ht=0|a[34],mt=0,pt=0|a[33],gt=0,bt=0|a[32],Ct=0,yt=0|a[31],kt=0,_t=0|a[30],At=0,vt=0|a[29],wt=0,Ot=0|a[28],xt=0,Nt=0|a[27],Mt=0,St=0|a[26],jt=0,Dt=0|a[25],Tt=0,Et=0|a[24],Pt=0,It=0|a[23],Ut=0,Wt=0|a[22],zt=0,Kt=0|a[21],Lt=0,Jt=0|a[20],Rt=0,Bt=0|a[19],Ft=0,$t=0|a[18],Vt=0,qt=0|a[17],Ht=0,Yt=0|a[16],Qt=0,Gt=0|a[15],Xt=0,Zt=0|a[14],te=0,ee=0|a[13],oe=0,ne=0|a[12],ae=0,re=0|a[11],ie=0,se=0|a[10],ce=0,ue=0|a[9],de=0,le=0|a[8],fe=0,he=0|a[7],me=0,pe=0|a[6],ge=0,be=0|a[5],Ce=0,ye=0|a[4],ke=0,_e=0|a[3],Ae=0,ve=0|a[2],we=0,Oe=0|a[1],xe=0,Ne=0|a[0];for(o=0;o<n;o++)s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=i<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,(e=t.charCodeAt(o))<128?i^=e:e<2048?(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=e>>6|192)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>12&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>6&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128):(s=397*u,d=397*l,f=397*h,p=397*b,C=397*y,k=397*_,A=397*v,w=397*O,x=397*N,M=397*S,j=397*D,T=397*E,P=397*I,U=397*W,z=397*K,L=397*J,R=397*B,F=397*$,V=397*q,H=397*Y,Q=397*G,X=397*Z,tt=397*et,ot=397*nt,at=397*rt,it=397*st,ct=397*ut,dt=397*lt,ft=397*ht,mt=397*pt,gt=397*bt,Ct=397*yt,kt=397*_t,At=397*vt,wt=397*Ot,xt=397*Nt,Mt=397*St,jt=397*Dt,Tt=397*Et,Pt=397*It,Ut=397*Wt,zt=397*Kt,Lt=397*Jt,Rt=397*Bt,Ft=397*$t,Vt=397*qt,Ht=397*Yt,Qt=397*Gt,Xt=397*Zt,te=397*ee,oe=397*ne,ae=397*re,ie=397*se,ce=397*ue,de=397*le,fe=397*he,me=397*pe,ge=397*be,Ce=397*ye,ke=397*_e,Ae=397*ve,we=397*Oe,xe=397*Ne,zt+=(i^=e>>12|224)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,i=65535&(r=397*i),Ne=(xe+=G<<8)+((we+=(Ae+=(ke+=(Ce+=(ge+=(me+=(fe+=(de+=(ce+=(ie+=(ae+=(oe+=(te+=(Xt+=(Qt+=(Ht+=(Vt+=(Ft+=(Rt+=(Lt+=(zt+=(Ut+=(Pt+=(Tt+=(jt+=(Mt+=(xt+=(wt+=(At+=(kt+=(Ct+=(gt+=(mt+=(ft+=(dt+=(ct+=(it+=(at+=(ot+=(tt+=(X+=(Q+=(H+=(V+=(F+=(R+=(L+=(z+=(U+=(P+=(T+=(j+=(M+=(x+=(w+=(A+=(k+=(C+=(p+=(f+=(d+=(s+=r>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)>>>16)&65535,s=397*(u=65535&s),d=397*(l=65535&d),f=397*(h=65535&f),p=397*(b=65535&p),C=397*(y=65535&C),k=397*(_=65535&k),A=397*(v=65535&A),w=397*(O=65535&w),x=397*(N=65535&x),M=397*(S=65535&M),j=397*(D=65535&j),T=397*(E=65535&T),P=397*(I=65535&P),U=397*(W=65535&U),z=397*(K=65535&z),L=397*(J=65535&L),R=397*(B=65535&R),F=397*($=65535&F),V=397*(q=65535&V),H=397*(Y=65535&H),Q=397*(G=65535&Q),X=397*(Z=65535&X),tt=397*(et=65535&tt),ot=397*(nt=65535&ot),at=397*(rt=65535&at),it=397*(st=65535&it),ct=397*(ut=65535&ct),dt=397*(lt=65535&dt),ft=397*(ht=65535&ft),mt=397*(pt=65535&mt),gt=397*(bt=65535&gt),Ct=397*(yt=65535&Ct),kt=397*(_t=65535&kt),At=397*(vt=65535&At),wt=397*(Ot=65535&wt),xt=397*(Nt=65535&xt),Mt=397*(St=65535&Mt),jt=397*(Dt=65535&jt),Tt=397*(Et=65535&Tt),Pt=397*(It=65535&Pt),Ut=397*(Wt=65535&Ut),zt=397*(Kt=65535&zt),Lt=397*(Jt=65535&Lt),Rt=397*(Bt=65535&Rt),Ft=397*($t=65535&Ft),Vt=397*(qt=65535&Vt),Ht=397*(Yt=65535&Ht),Qt=397*(Gt=65535&Qt),Xt=397*(Zt=65535&Xt),te=397*(ee=65535&te),oe=397*(ne=65535&oe),ae=397*(re=65535&ae),ie=397*(se=65535&ie),ce=397*(ue=65535&ce),de=397*(le=65535&de),fe=397*(he=65535&fe),me=397*(pe=65535&me),ge=397*(be=65535&ge),Ce=397*(ye=65535&Ce),ke=397*(_e=65535&ke),Ae=397*(ve=65535&Ae),we=397*(Oe=65535&we),xe=397*Ne,zt+=(i^=e>>6&63|128)<<8,Lt+=u<<8,Rt+=l<<8,Ft+=h<<8,Vt+=b<<8,Ht+=y<<8,Qt+=_<<8,Xt+=v<<8,te+=O<<8,oe+=N<<8,ae+=S<<8,ie+=D<<8,ce+=E<<8,de+=I<<8,fe+=W<<8,me+=K<<8,ge+=J<<8,Ce+=B<<8,ke+=$<<8,Ae+=q<<8,we+=Y<<8,xe+=G<<8,i=65535&(r=397*i),u=65535&(s+=r>>>16),l=65535&(d+=s>>>16),h=65535&(f+=d>>>16),b=65535&(p+=f>>>16),y=65535&(C+=p>>>16),_=65535&(k+=C>>>16),v=65535&(A+=k>>>16),O=65535&(w+=A>>>16),N=65535&(x+=w>>>16),S=65535&(M+=x>>>16),D=65535&(j+=M>>>16),E=65535&(T+=j>>>16),I=65535&(P+=T>>>16),W=65535&(U+=P>>>16),K=65535&(z+=U>>>16),J=65535&(L+=z>>>16),B=65535&(R+=L>>>16),$=65535&(F+=R>>>16),q=65535&(V+=F>>>16),Y=65535&(H+=V>>>16),G=65535&(Q+=H>>>16),Z=65535&(X+=Q>>>16),et=65535&(tt+=X>>>16),nt=65535&(ot+=tt>>>16),rt=65535&(at+=ot>>>16),st=65535&(it+=at>>>16),ut=65535&(ct+=it>>>16),lt=65535&(dt+=ct>>>16),ht=65535&(ft+=dt>>>16),pt=65535&(mt+=ft>>>16),bt=65535&(gt+=mt>>>16),yt=65535&(Ct+=gt>>>16),_t=65535&(kt+=Ct>>>16),vt=65535&(At+=kt>>>16),Ot=65535&(wt+=At>>>16),Nt=65535&(xt+=wt>>>16),St=65535&(Mt+=xt>>>16),Dt=65535&(jt+=Mt>>>16),Et=65535&(Tt+=jt>>>16),It=65535&(Pt+=Tt>>>16),Wt=65535&(Ut+=Pt>>>16),Kt=65535&(zt+=Ut>>>16),Jt=65535&(Lt+=zt>>>16),Bt=65535&(Rt+=Lt>>>16),$t=65535&(Ft+=Rt>>>16),qt=65535&(Vt+=Ft>>>16),Yt=65535&(Ht+=Vt>>>16),Gt=65535&(Qt+=Ht>>>16),Zt=65535&(Xt+=Qt>>>16),ee=65535&(te+=Xt>>>16),ne=65535&(oe+=te>>>16),re=65535&(ae+=oe>>>16),se=65535&(ie+=ae>>>16),ue=65535&(ce+=ie>>>16),le=65535&(de+=ce>>>16),he=65535&(fe+=de>>>16),pe=65535&(me+=fe>>>16),be=65535&(ge+=me>>>16),ye=65535&(Ce+=ge>>>16),_e=65535&(ke+=Ce>>>16),ve=65535&(Ae+=ke>>>16),Ne=xe+((we+=Ae>>>16)>>>16)&65535,Oe=65535&we,i^=63&e|128);return g(c[Ne>>8]+c[255&Ne]+c[Oe>>8]+c[255&Oe]+c[ve>>8]+c[255&ve]+c[_e>>8]+c[255&_e]+c[ye>>8]+c[255&ye]+c[be>>8]+c[255&be]+c[pe>>8]+c[255&pe]+c[he>>8]+c[255&he]+c[le>>8]+c[255&le]+c[ue>>8]+c[255&ue]+c[se>>8]+c[255&se]+c[re>>8]+c[255&re]+c[ne>>8]+c[255&ne]+c[ee>>8]+c[255&ee]+c[Zt>>8]+c[255&Zt]+c[Gt>>8]+c[255&Gt]+c[Yt>>8]+c[255&Yt]+c[qt>>8]+c[255&qt]+c[$t>>8]+c[255&$t]+c[Bt>>8]+c[255&Bt]+c[Jt>>8]+c[255&Jt]+c[Kt>>8]+c[255&Kt]+c[Wt>>8]+c[255&Wt]+c[It>>8]+c[255&It]+c[Et>>8]+c[255&Et]+c[Dt>>8]+c[255&Dt]+c[St>>8]+c[255&St]+c[Nt>>8]+c[255&Nt]+c[Ot>>8]+c[255&Ot]+c[vt>>8]+c[255&vt]+c[_t>>8]+c[255&_t]+c[yt>>8]+c[255&yt]+c[bt>>8]+c[255&bt]+c[pt>>8]+c[255&pt]+c[ht>>8]+c[255&ht]+c[lt>>8]+c[255&lt]+c[ut>>8]+c[255&ut]+c[st>>8]+c[255&st]+c[rt>>8]+c[255&rt]+c[nt>>8]+c[255&nt]+c[et>>8]+c[255&et]+c[Z>>8]+c[255&Z]+c[G>>8]+c[255&G]+c[Y>>8]+c[255&Y]+c[q>>8]+c[255&q]+c[$>>8]+c[255&$]+c[B>>8]+c[255&B]+c[J>>8]+c[255&J]+c[K>>8]+c[255&K]+c[W>>8]+c[255&W]+c[I>>8]+c[255&I]+c[E>>8]+c[255&E]+c[D>>8]+c[255&D]+c[S>>8]+c[255&S]+c[N>>8]+c[255&N]+c[O>>8]+c[255&O]+c[v>>8]+c[255&v]+c[_>>8]+c[255&_]+c[y>>8]+c[255&y]+c[b>>8]+c[255&b]+c[h>>8]+c[255&h]+c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i],1024)}return e=v,o=N,n=D,a=I,r=K,i=B,s=q,k("1a"),_(!1),A(),{hash:y,setKeyspace:function(t){if(52!==t&&!m[t])throw new Error("Supported FNV keyspacs: 32, 52, 64, 128, 256, 512, and 1024 bit");h=t},version:k,useUTF8:_,seed:A,fast1a32:function(t){var e,o=t.length-3,n=0,a=40389,r=0,i=33052;for(e=0;e<o;)r=403*i,r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),i=(r+=(a^=t.charCodeAt(e++))<<8)+((n=403*a)>>>16)&65535,a=65535&n;for(;e<o+3;)r=403*i,i=(r+=(a^=t.charCodeAt(e++))<<8)+((n=403*a)>>>16)&65535,a=65535&n;return(i<<16>>>0)+a},fast1a32hex:function(t){var e,o=t.length-3,n=0,a=40389,r=0,i=33052;for(e=0;e<o;)r=403*i,r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),r+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=403*a),r=403*(i=r+(n>>>16)&65535),i=(r+=(a^=t.charCodeAt(e++))<<8)+((n=403*a)>>>16)&65535,a=65535&n;for(;e<o+3;)r=403*i,i=(r+=(a^=t.charCodeAt(e++))<<8)+((n=403*a)>>>16)&65535,a=65535&n;return c[i>>>8&255]+c[255&i]+c[a>>>8&255]+c[255&a]},fast1a52:function(t){var e,o=t.length-3,n=0,a=8997,r=0,i=33826,s=0,c=40164,u=0,d=52210;for(e=0;e<o;)r=435*i,s=435*c,u=435*d,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),d=(u+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(c=65535&s),u=435*d,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),d=(u+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(c=65535&s),u=435*d,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),d=(u+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(c=65535&s),u=435*d,s+=(a^=t.charCodeAt(e++))<<8,u+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),d=u+((s+=r>>>16)>>>16)&65535,c=65535&s;for(;e<o+3;)r=435*i,s=435*c,u=435*d,s+=(a^=t.charCodeAt(e++))<<8,u+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),d=u+((s+=r>>>16)>>>16)&65535,c=65535&s;return 281474976710656*(15&d)+4294967296*c+65536*i+(a^d>>4)},fast1a52hex:function(t){var e,o=t.length-3,n=0,a=8997,r=0,i=33826,s=0,d=40164,l=0,f=52210;for(e=0;e<o;)r=435*i,s=435*d,l=435*f,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),f=(l+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(d=65535&s),l=435*f,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),f=(l+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(d=65535&s),l=435*f,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),f=(l+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(d=65535&s),l=435*f,s+=(a^=t.charCodeAt(e++))<<8,l+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),f=l+((s+=r>>>16)>>>16)&65535,d=65535&s;for(;e<o+3;)r=435*i,s=435*d,l=435*f,s+=(a^=t.charCodeAt(e++))<<8,l+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),f=l+((s+=r>>>16)>>>16)&65535,d=65535&s;return u[15&f]+c[d>>8]+c[255&d]+c[i>>8]+c[255&i]+c[a>>8^f>>12]+c[255&(a^f>>4)]},fast1a64:function(t){var e,o=t.length-3,n=0,a=8997,r=0,i=33826,s=0,u=40164,d=0,l=52210;for(e=0;e<o;)r=435*i,s=435*u,d=435*l,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),l=(d+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(u=65535&s),d=435*l,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),l=(d+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(u=65535&s),d=435*l,s+=(a^=t.charCodeAt(e++))<<8,a=65535&(n=435*a),l=(d+=i<<8)+((s+=(r+=n>>>16)>>>16)>>>16)&65535,r=435*(i=65535&r),s=435*(u=65535&s),d=435*l,s+=(a^=t.charCodeAt(e++))<<8,d+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),l=d+((s+=r>>>16)>>>16)&65535,u=65535&s;for(;e<o+3;)r=435*i,s=435*u,d=435*l,s+=(a^=t.charCodeAt(e++))<<8,d+=i<<8,a=65535&(n=435*a),i=65535&(r+=n>>>16),l=d+((s+=r>>>16)>>>16)&65535,u=65535&s;return c[l>>8]+c[255&l]+c[u>>8]+c[255&u]+c[i>>8]+c[255&i]+c[a>>8]+c[255&a]},fast1a32utf:function(t){var e,o,n=t.length,a=0,r=40389,i=0,s=33052;for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?r^=e:e<2048?(i=403*s,s=(i+=(r^=e>>6|192)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(i=403*s,i+=(r^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=e>>12&63|128)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=e>>6&63|128)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128):(i=403*s,i+=(r^=e>>12|224)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=e>>6&63|128)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128),i=403*s,s=(i+=r<<8)+((a=403*r)>>>16)&65535,r=65535&a;return(s<<16>>>0)+r},fast1a32hexutf:function(t){var e,o,n=t.length,a=0,r=40389,i=0,s=33052;for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?r^=e:e<2048?(i=403*s,s=(i+=(r^=e>>6|192)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(i=403*s,i+=(r^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),i+=(r^=e>>12&63|128)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=e>>6&63|128)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128):(i=403*s,i+=(r^=e>>12|224)<<8,r=65535&(a=403*r),i=403*(s=i+(a>>>16)&65535),s=(i+=(r^=e>>6&63|128)<<8)+((a=403*r)>>>16)&65535,r=65535&a,r^=63&e|128),i=403*s,s=(i+=r<<8)+((a=403*r)>>>16)&65535,r=65535&a;return c[s>>>8&255]+c[255&s]+c[r>>>8&255]+c[255&r]},fast1a52utf:function(t){var e,o,n=t.length,a=0,r=8997,i=0,s=33826,c=0,u=40164,d=0,l=52210;for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?r^=e:e<2048?(i=435*s,c=435*u,d=435*l,c+=(r^=e>>6|192)<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c,r^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(i=435*s,c=435*u,d=435*l,c+=(r^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=e>>12&63|128)<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=e>>6&63|128)<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c,r^=63&e|128):(i=435*s,c=435*u,d=435*l,c+=(r^=e>>12|224)<<8,r=65535&(a=435*r),l=(d+=s<<8)+((c+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),c=435*(u=65535&c),d=435*l,c+=(r^=e>>6&63|128)<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c,r^=63&e|128),i=435*s,c=435*u,d=435*l,c+=r<<8,d+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),l=d+((c+=i>>>16)>>>16)&65535,u=65535&c;return 281474976710656*(15&l)+4294967296*u+65536*s+(r^l>>4)},fast1a52hexutf:function(t){var e,o,n=t.length,a=0,r=8997,i=0,s=33826,d=0,l=40164,f=0,h=52210;for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?r^=e:e<2048?(i=435*s,d=435*l,f=435*h,d+=(r^=e>>6|192)<<8,f+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),h=f+((d+=i>>>16)>>>16)&65535,l=65535&d,r^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(i=435*s,d=435*l,f=435*h,d+=(r^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,r=65535&(a=435*r),h=(f+=s<<8)+((d+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),d=435*(l=65535&d),f=435*h,d+=(r^=e>>12&63|128)<<8,r=65535&(a=435*r),h=(f+=s<<8)+((d+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),d=435*(l=65535&d),f=435*h,d+=(r^=e>>6&63|128)<<8,f+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),h=f+((d+=i>>>16)>>>16)&65535,l=65535&d,r^=63&e|128):(i=435*s,d=435*l,f=435*h,d+=(r^=e>>12|224)<<8,r=65535&(a=435*r),h=(f+=s<<8)+((d+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),d=435*(l=65535&d),f=435*h,d+=(r^=e>>6&63|128)<<8,f+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),h=f+((d+=i>>>16)>>>16)&65535,l=65535&d,r^=63&e|128),i=435*s,d=435*l,f=435*h,d+=r<<8,f+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),h=f+((d+=i>>>16)>>>16)&65535,l=65535&d;return u[15&h]+c[l>>8]+c[255&l]+c[s>>8]+c[255&s]+c[r>>8^h>>12]+c[255&(r^h>>4)]},fast1a64utf:function(t){var e,o,n=t.length,a=0,r=8997,i=0,s=33826,u=0,d=40164,l=0,f=52210;for(o=0;o<n;o++)(e=t.charCodeAt(o))<128?r^=e:e<2048?(i=435*s,u=435*d,l=435*f,u+=(r^=e>>6|192)<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u,r^=63&e|128):55296==(64512&e)&&o+1<n&&56320==(64512&t.charCodeAt(o+1))?(i=435*s,u=435*d,l=435*f,u+=(r^=(e=65536+((1023&e)<<10)+(1023&t.charCodeAt(++o)))>>18|240)<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=e>>12&63|128)<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=e>>6&63|128)<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u,r^=63&e|128):(i=435*s,u=435*d,l=435*f,u+=(r^=e>>12|224)<<8,r=65535&(a=435*r),f=(l+=s<<8)+((u+=(i+=a>>>16)>>>16)>>>16)&65535,i=435*(s=65535&i),u=435*(d=65535&u),l=435*f,u+=(r^=e>>6&63|128)<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u,r^=63&e|128),i=435*s,u=435*d,l=435*f,u+=r<<8,l+=s<<8,r=65535&(a=435*r),s=65535&(i+=a>>>16),f=l+((u+=i>>>16)>>>16)&65535,d=65535&u;return c[f>>8]+c[255&f]+c[d>>8]+c[255&d]+c[s>>8]+c[255&s]+c[r>>8]+c[255&r]}}}();void 0!==t.exports&&(t.exports=e)},855:t=>{"use strict";const e=/^(?:( )+|\t+)/,o="space";function n(t,n){const r=new Map;let i,s,c=0;for(const u of t.split(/\n/g)){if(!u)continue;let t,d,l,f;const h=u.match(e);if(null===h)c=0,i="";else{if(t=h[0].length,d=h[1]?o:"tab",n&&d===o&&1===t)continue;d!==i&&(c=0),i=d,l=0;const e=t-c;c=t,0===e?l++:s=a(d,e>0?e:-e),f=r.get(s),f=void 0===f?[1,0]:[++f[0],f[1]+l],r.set(s,f)}}return r}function a(t,e){return(t===o?"s":"t")+String(e)}t.exports=t=>{if("string"!=typeof t)throw new TypeError("Expected a string");let e=n(t,!0);0===e.size&&(e=n(t,!1));const a=function(t){let e,o=0,n=0;for(const[a,[r,i]]of t)(r>o||r===o&&i>n)&&(o=r,n=i,e=a);return e}(e);let r,i=0,s="";var c;return void 0!==a&&(({type:r,amount:i}={type:"s"===(c=a)[0]?o:"tab",amount:Number(c.slice(1))}),s=function(t,e){return(t===o?" ":"\t").repeat(e)}(r,i)),{amount:i,type:r,indent:s}}},712:(t,e)=>{"use strict";var o;Object.defineProperty(e,"__esModule",{value:!0}),e.textMimeTypes=e.CellOutputMimeTypes=e.NotebookCellKindCode=e.NotebookCellKindMarkup=e.JUPYTER_NOTEBOOK_MARKDOWN_SELECTOR=e.ATTACHMENT_CLEANUP_COMMANDID=e.defaultNotebookFormat=void 0,e.defaultNotebookFormat={major:4,minor:5},e.ATTACHMENT_CLEANUP_COMMANDID="ipynb.cleanInvalidImageAttachment",e.JUPYTER_NOTEBOOK_MARKDOWN_SELECTOR={notebookType:"jupyter-notebook",language:"markdown"},e.NotebookCellKindMarkup=1,e.NotebookCellKindCode=2,function(t){t.error="application/vnd.code.notebook.error",t.stderr="application/vnd.code.notebook.stderr",t.stdout="application/vnd.code.notebook.stdout"}(o||(e.CellOutputMimeTypes=o={})),e.textMimeTypes=["text/plain","text/markdown","text/latex",o.stderr,o.stdout]},929:(t,e,o)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getPreferredLanguage=function(t){const e=t?.language_info?.name||t?.kernelspec?.language,o=n.extensions.getExtension("ms-python.python")?"python":n.extensions.getExtension("ms-dotnettools.dotnet-interactive-vscode")?"csharp":"python";return 2===(a=(a=e||o).toLowerCase()).length&&a.endsWith("#")?`${a.substring(0,1)}sharp`:r.get(a)||a;var a},e.jupyterCellOutputToCellOutput=h,e.jupyterNotebookModelToNotebookData=function(t,e){const o={...t,cells:[]};if(!Array.isArray(t.cells))throw new Error("Notebook content is missing cells");const a=t.cells.map((t=>function(t,e){switch(e.cell_type){case"raw":return function(t){const e=new n.NotebookCellData(n.NotebookCellKind.Code,s(t.source),"raw");return e.outputs=[],e.metadata=u(t),e}(e);case"markdown":return function(t){const e=new n.NotebookCellData(n.NotebookCellKind.Markup,s(t.source),"markdown");return e.outputs=[],e.metadata=u(t),e}(e);case"code":return function(t,e){const o=(Array.isArray(t.outputs)?t.outputs:[]).map(h),a="number"==typeof t.execution_count&&t.execution_count>0,r=s(t.source),i=a?{executionOrder:t.execution_count}:{},c=t.metadata.vscode,d=c&&c.languageId&&"string"==typeof c.languageId?c.languageId:e,l=new n.NotebookCellData(n.NotebookCellKind.Code,r,d);return l.outputs=o,l.metadata=u(t),l.executionSummary=i,l}(e,t)}}(e,t))).filter((t=>!!t)),r=new n.NotebookData(a);return r.metadata=o,r};const n=o(398),a=o(712),r=new Map([["c#","csharp"],["f#","fsharp"],["q#","qsharp"],["c++11","c++"],["c++12","c++"],["c++14","c++"]]),i=["application/vnd.*","application/vdom.*","application/geo+json","application/x-nteract-model-debug+json","text/html","application/javascript","image/gif","text/latex","text/markdown","image/png","image/svg+xml","image/jpeg","application/json","text/plain"];function s(t,e){const o=/(^[\t\f\v\r ]+|[\t\f\v\r ]+$)/g;if(Array.isArray(t)){let n="";for(let e=0;e<t.length;e+=1){const o=t[e];n=e<t.length-1&&!o.endsWith("\n")?n.concat(`${o}\n`):n.concat(o)}return e?n.replace(o,""):n}return e?t.toString().replace(o,""):t.toString()}function c(t,e){if(!e)return n.NotebookCellOutputItem.text("",t);try{if(!t.startsWith("text/")&&!a.textMimeTypes.includes(t)||!Array.isArray(e)&&"string"!=typeof e){if(t.startsWith("image/")&&"string"==typeof e&&"image/svg+xml"!==t){if("undefined"!=typeof Buffer&&"function"==typeof Buffer.from)return new n.NotebookCellOutputItem(Buffer.from(e,"base64"),t);{const o=Uint8Array.from(atob(e),(t=>t.charCodeAt(0)));return new n.NotebookCellOutputItem(o,t)}}return"object"!=typeof e||null===e||Array.isArray(e)?"application/json"===t?n.NotebookCellOutputItem.json(e,t):(e=Array.isArray(e)?s(e):e,n.NotebookCellOutputItem.text(e,t)):n.NotebookCellOutputItem.text(JSON.stringify(e),t)}{const o=Array.isArray(e)?s(e):e;return n.NotebookCellOutputItem.text(o,t)}}catch(t){return n.NotebookCellOutputItem.error(t)}}function u(t){const e={};return"code"===t.cell_type&&("number"==typeof t.execution_count?e.execution_count=t.execution_count:e.execution_count=null),t.metadata&&(e.metadata=JSON.parse(JSON.stringify(t.metadata))),"id"in t&&"string"==typeof t.id&&(e.id=t.id),t.attachments&&(e.attachments=JSON.parse(JSON.stringify(t.attachments))),e}function d(t){const e={outputType:t.output_type};switch(t.transient&&(e.transient=t.transient),t.output_type){case"display_data":case"execute_result":case"update_display_data":e.executionCount=t.execution_count,e.metadata=t.metadata?JSON.parse(JSON.stringify(t.metadata)):{}}return e}function l(t){const e=d(t),o=[];if(t.data)for(const e in t.data)o.push(c(e,t.data[e]));return new n.NotebookCellOutput(o.map((t=>{let e=i.findIndex((e=>{return o=e,n=t.mime,o.endsWith(".*")&&(o=o.substr(0,o.indexOf(".*"))),n.startsWith(o);var o,n}));return function(t){if(t.mime.startsWith("application/vnd."))try{return 0===t.data.byteLength||0===Buffer.from(t.data).toString().length}catch{}return!1}(t)&&(e=-1),e=-1===e?100:e,{item:t,index:e}})).sort(((t,e)=>t.index-e.index)).map((t=>t.item)),e)}const f=new Map;function h(t){const e=f.get(t.output_type);let o;return o=e?e(t):l(t),o}f.set("display_data",l),f.set("execute_result",l),f.set("update_display_data",l),f.set("error",(function(t){return t=t||{output_type:"error",ename:"",evalue:"",traceback:[]},new n.NotebookCellOutput([n.NotebookCellOutputItem.error({name:t?.ename||"",message:t?.evalue||"",stack:(t?.traceback||[]).join("\n")})],{...d(t),originalError:t})})),f.set("stream",(function(t){const e=s(t.text),o="stderr"===t.name?n.NotebookCellOutputItem.stderr(e):n.NotebookCellOutputItem.stdout(e);return new n.NotebookCellOutput([o],d(t))}))},829:(t,e,o)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DeferredPromise=e.Delayer=void 0,e.deepClone=function t(e){if(!e||"object"!=typeof e)return e;if(e instanceof RegExp)return e;const o=Array.isArray(e)?[]:{};return Object.keys(e).forEach((n=>{e[n]&&"object"==typeof e[n]?o[n]=t(e[n]):o[n]=e[n]})),o},e.objectEquals=function t(e,o){if(e===o)return!0;if(null==e||null==o)return!1;if(typeof e!=typeof o)return!1;if("object"!=typeof e)return!1;if(Array.isArray(e)!==Array.isArray(o))return!1;let n,a;if(Array.isArray(e)){if(e.length!==o.length)return!1;for(n=0;n<e.length;n++)if(!t(e[n],o[n]))return!1}else{const r=[];for(a in e)r.push(a);r.sort();const i=[];for(a in o)i.push(a);if(i.sort(),!t(r,i))return!1;for(n=0;n<r.length;n++)if(!t(e[r[n]],o[r[n]]))return!1}return!0},e.generateUuid=function(){if("function"==typeof crypto.randomUUID)return crypto.randomUUID.bind(crypto)();const t=new Uint8Array(16),e=[];for(let t=0;t<256;t++)e.push(t.toString(16).padStart(2,"0"));crypto.getRandomValues(t),t[6]=15&t[6]|64,t[8]=63&t[8]|128;let o=0,n="";return n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n+="-",n+=e[t[o++]],n+=e[t[o++]],n+="-",n+=e[t[o++]],n+=e[t[o++]],n+="-",n+=e[t[o++]],n+=e[t[o++]],n+="-",n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n+=e[t[o++]],n};const n=o(398);e.Delayer=class{constructor(t){this.defaultDelay=t,this._timeout=null,this._cancelTimeout=null,this._onSuccess=null,this._task=null}dispose(){this._doCancelTimeout()}trigger(t,e=this.defaultDelay){return this._task=t,e>=0&&this._doCancelTimeout(),this._cancelTimeout||(this._cancelTimeout=new Promise((t=>{this._onSuccess=t})).then((()=>{this._cancelTimeout=null,this._onSuccess=null;const t=this._task&&this._task?.();return this._task=null,t}))),(e>=0||null===this._timeout)&&(this._timeout=setTimeout((()=>{this._timeout=null,this._onSuccess?.(void 0)}),e>=0?e:this.defaultDelay)),this._cancelTimeout}_doCancelTimeout(){null!==this._timeout&&(clearTimeout(this._timeout),this._timeout=null)}},e.DeferredPromise=class{get isRejected(){return 1===this.outcome?.outcome}get isResolved(){return 0===this.outcome?.outcome}get isSettled(){return!!this.outcome}get value(){return 0===this.outcome?.outcome?this.outcome?.value:void 0}constructor(){this.p=new Promise(((t,e)=>{this.completeCallback=t,this.errorCallback=e}))}complete(t){return new Promise((e=>{this.completeCallback(t),this.outcome={outcome:0,value:t},e()}))}error(t){return new Promise((e=>{this.errorCallback(t),this.outcome={outcome:1,value:t},e()}))}cancel(){return this.error(new n.CancellationError)}}},772:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.activate=function(t){return s.activate(t,new c.NotebookSerializer(t))},e.deactivate=function(){return s.deactivate()};const s=i(o(192)),c=o(292)},192:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.activate=function(t,e){if((0,c.activate)(t),t.subscriptions.push(s.workspace.registerNotebookSerializer("jupyter-notebook",e,{transientOutputs:!1,transientDocumentMetadata:{cells:!0,indentAmount:!0},transientCellMetadata:{breakpointMargin:!0,id:!1,metadata:!1,attachments:!1},cellContentMetadata:{attachments:!0}})),t.subscriptions.push(s.workspace.registerNotebookSerializer("interactive",e,{transientOutputs:!1,transientCellMetadata:{breakpointMargin:!0,id:!1,metadata:!1,attachments:!1},cellContentMetadata:{attachments:!0}})),s.languages.registerCodeLensProvider({pattern:"**/*.ipynb"},{provideCodeLenses:t=>"vscode-notebook-cell"===t.uri.scheme||"vscode-notebook-cell-metadata"===t.uri.scheme||"vscode-notebook-cell-output"===t.uri.scheme?[]:[new s.CodeLens(new s.Range(0,0,0,0),{title:"Open in Notebook Editor",command:"ipynb.openIpynbInNotebookEditor",arguments:[t.uri]})]}),t.subscriptions.push(s.commands.registerCommand("ipynb.newUntitledIpynb",(async()=>{const t=new s.NotebookCellData(s.NotebookCellKind.Code,"","python"),e=new s.NotebookData([t]);e.metadata={cells:[],metadata:{},nbformat:f.defaultNotebookFormat.major,nbformat_minor:f.defaultNotebookFormat.minor};const o=await s.workspace.openNotebookDocument("jupyter-notebook",e);await s.window.showNotebookDocument(o)}))),t.subscriptions.push(s.commands.registerCommand("ipynb.openIpynbInNotebookEditor",(async t=>{s.window.activeTextEditor?.document.uri.toString()===t.toString()&&await s.commands.executeCommand("workbench.action.closeActiveEditor");const e=await s.workspace.openNotebookDocument(t);await s.window.showNotebookDocument(e)}))),t.subscriptions.push((0,u.notebookImagePasteSetup)()),s.workspace.getConfiguration("ipynb").get("pasteImagesAsAttachments.enabled",!1)){const e=new d.AttachmentCleaner;t.subscriptions.push(e)}return{get dropCustomMetadata(){return!0},exportNotebook:t=>Promise.resolve((0,l.serializeNotebookToString)(t)),setNotebookMetadata:async(t,e)=>{const o=s.workspace.notebookDocuments.find((e=>e.uri.toString()===t.toString()));if(!o)return!1;const n=new s.WorkspaceEdit;return n.set(t,[s.NotebookEdit.updateNotebookMetadata({...o.metadata,metadata:{...o.metadata.metadata??{},...e}})]),s.workspace.applyEdit(n)}}},e.deactivate=function(){};const s=i(o(398)),c=o(269),u=o(868),d=o(723),l=o(624),f=o(712)},723:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.AttachmentCleaner=e.DiagnosticCode=void 0;const s=i(o(398)),c=o(712),u=o(829);var d;!function(t){t.missing_attachment="notebook.missing-attachment"}(d||(e.DiagnosticCode=d={})),e.AttachmentCleaner=class{constructor(){this._attachmentCache=new Map,this._delayer=new u.Delayer(750),this._disposables=[],this._imageDiagnosticCollection=s.languages.createDiagnosticCollection("Notebook Image Attachment"),this._disposables.push(this._imageDiagnosticCollection),this._disposables.push(s.commands.registerCommand(c.ATTACHMENT_CLEANUP_COMMANDID,(async(t,e)=>{const o=new s.WorkspaceEdit;o.delete(t,e),await s.workspace.applyEdit(o)}))),this._disposables.push(s.languages.registerCodeActionsProvider(c.JUPYTER_NOTEBOOK_MARKDOWN_SELECTOR,this,{providedCodeActionKinds:[s.CodeActionKind.QuickFix]})),this._disposables.push(s.workspace.onDidChangeNotebookDocument((t=>{this._delayer.trigger((()=>{t.cellChanges.forEach((e=>{if(!e.document)return;if(e.cell.kind!==s.NotebookCellKind.Markup)return;const o=this.cleanNotebookAttachments({notebook:t.notebook,cell:e.cell,document:e.document});if(o){const e=new s.WorkspaceEdit;e.set(t.notebook.uri,[o]),s.workspace.applyEdit(e)}}))}))}))),this._disposables.push(s.workspace.onWillSaveNotebookDocument((t=>{if(t.reason===s.TextDocumentSaveReason.Manual){if(this._delayer.dispose(),0===t.notebook.getCells().length)return;const e=[];for(const o of t.notebook.getCells()){if(o.kind!==s.NotebookCellKind.Markup)continue;const n=this.cleanNotebookAttachments({notebook:t.notebook,cell:o,document:o.document});n&&e.push(n)}if(!e.length)return;const o=new s.WorkspaceEdit;o.set(t.notebook.uri,e),t.waitUntil(Promise.resolve(o))}}))),this._disposables.push(s.workspace.onDidCloseNotebookDocument((t=>{this._attachmentCache.delete(t.uri.toString())}))),this._disposables.push(s.workspace.onWillRenameFiles((t=>{const e=/\.ipynb$/;for(const o of t.files)e.exec(o.oldUri.toString())&&this._attachmentCache.has(o.oldUri.toString())&&(this._attachmentCache.set(o.newUri.toString(),this._attachmentCache.get(o.oldUri.toString())),this._attachmentCache.delete(o.oldUri.toString()))}))),this._disposables.push(s.workspace.onDidOpenTextDocument((t=>{this.analyzeMissingAttachments(t)}))),this._disposables.push(s.workspace.onDidCloseTextDocument((t=>{this.analyzeMissingAttachments(t)}))),s.workspace.textDocuments.forEach((t=>{this.analyzeMissingAttachments(t)}))}provideCodeActions(t,e,o,n){const a=[];for(const e of o.diagnostics)if(e.code===d.missing_attachment){const o=new s.CodeAction("Remove invalid image attachment reference",s.CodeActionKind.QuickFix);o.command={command:c.ATTACHMENT_CLEANUP_COMMANDID,title:"Remove invalid image attachment reference",arguments:[t.uri,e.range]},a.push(o)}return a}cleanNotebookAttachments(t){if(t.notebook.isClosed)return;const e=t.document,o=t.cell,n={},a=o.document.uri.fragment,r=t.notebook.uri.toString(),i=[],c=this.getAttachmentNames(e);if(0===c.size&&this.saveAllAttachmentsToCache(o.metadata,r,a),this.checkMetadataHasAttachmentsField(o.metadata))for(const[t,e]of Object.entries(o.metadata.attachments))c.has(t)?(c.get(t).valid=!0,n[t]=e):this.saveAttachmentToCache(r,a,t,o.metadata);for(const[t,e]of c){if(e.valid)continue;const o=this._attachmentCache.get(r)?.get(a)?.get(t);o?(n[t]=o,this._attachmentCache.get(r)?.get(a)?.delete(t)):i.push({name:t,ranges:e.ranges})}if(this.updateDiagnostics(o.document.uri,i),o.index>-1&&!(0,u.objectEquals)(n||{},o.metadata.attachments||{})){const t=(0,u.deepClone)(o.metadata);return 0===Object.keys(n).length?t.attachments=void 0:t.attachments=n,s.NotebookEdit.updateCellMetadata(o.index,t)}}analyzeMissingAttachments(t){if("vscode-notebook-cell"!==t.uri.scheme)return;if(t.isClosed)return void this.updateDiagnostics(t.uri,[]);let e,o;for(const n of s.workspace.notebookDocuments){const a=n.getCells().find((e=>e.document===t));if(a){e=n,o=a;break}}if(!e||!o)return;const n=[],a=this.getAttachmentNames(t);if(this.checkMetadataHasAttachmentsField(o.metadata))for(const[t,e]of a)o.metadata.attachments[t]||n.push({name:t,ranges:e.ranges});this.updateDiagnostics(o.document.uri,n)}updateDiagnostics(t,e){const o=[];for(const t of e)t.ranges.forEach((e=>{const n=new s.Diagnostic(e,`The image named: '${t.name}' is not present in cell metadata.`,s.DiagnosticSeverity.Warning);n.code=d.missing_attachment,o.push(n)}));this._imageDiagnosticCollection.set(t,o)}saveAttachmentToCache(t,e,o,n){const a=this._attachmentCache.get(t);if(a)if(a.has(e))a.get(e)?.set(o,this.getMetadataAttachment(n,o));else{const t=new Map;t.set(o,this.getMetadataAttachment(n,o)),a.set(e,t)}else{const a=new Map;a.set(o,this.getMetadataAttachment(n,o));const r=new Map;r.set(e,a),this._attachmentCache.set(t,r)}}getMetadataAttachment(t,e){return t.attachments[e]}checkMetadataHasAttachmentsField(t){return!!t.attachments&&"object"==typeof t.attachments}saveAllAttachmentsToCache(t,e,o){const n=this._attachmentCache.get(e)??new Map;this._attachmentCache.set(e,n);const a=n.get(o)??new Map;if(n.set(o,a),t.attachments&&"object"==typeof t.attachments)for(const[e,o]of Object.entries(t.attachments))a.set(e,o)}getAttachmentNames(t){const e=t.getText(),o=new Map,n=/!\[.*?\]\(<?attachment:(?<filename>.*?)>?\)/gm;let a;for(;a=n.exec(e);)if(a.groups?.filename){const e=a.index,n=a[0].length,r=t.positionAt(e),i=t.positionAt(e+n),c=new s.Range(r,i),u=o.get(a.groups.filename)??{valid:!1,ranges:[]};o.set(a.groups.filename,u),u.ranges.push(c)}return o}dispose(){this._disposables.forEach((t=>t.dispose())),this._delayer.dispose()}}},868:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.notebookImagePasteSetup=function(){const t=new h;return s.Disposable.from(s.languages.registerDocumentPasteEditProvider(c.JUPYTER_NOTEBOOK_MARKDOWN_SELECTOR,t,{providedPasteEditKinds:[h.kind],pasteMimeTypes:[d.png,d.uriList]}),s.languages.registerDocumentDropEditProvider(c.JUPYTER_NOTEBOOK_MARKDOWN_SELECTOR,t,{providedDropEditKinds:[h.kind],dropMimeTypes:[...Object.values(f),d.uriList]}))};const s=i(o(398)),c=o(712),u=o(928);var d;!function(t){t.bmp="image/bmp",t.gif="image/gif",t.ico="image/ico",t.jpeg="image/jpeg",t.png="image/png",t.tiff="image/tiff",t.webp="image/webp",t.plain="text/plain",t.uriList="text/uri-list"}(d||(d={}));const l=new Set([d.bmp,d.gif,d.ico,d.jpeg,d.png,d.tiff,d.webp]),f=new Map([[".bmp",d.bmp],[".gif",d.gif],[".ico",d.ico],[".jpe",d.jpeg],[".jpeg",d.jpeg],[".jpg",d.jpeg],[".png",d.png],[".tif",d.tiff],[".tiff",d.tiff],[".webp",d.webp]]);class h{async provideDocumentPasteEdits(t,e,o,n,a){if(!s.workspace.getConfiguration("ipynb",t).get("pasteImagesAsAttachments.enabled",!0))return;const r=await this.createInsertImageAttachmentEdit(t,o,a);if(!r)return;const i=new s.DocumentPasteEdit(r.insertText,s.l10n.t("Insert Image as Attachment"),h.kind);return i.yieldTo=[s.DocumentDropOrPasteEditKind.Text],i.additionalEdit=r.additionalEdit,[i]}async provideDocumentDropEdits(t,e,o,n){const a=await this.createInsertImageAttachmentEdit(t,o,n);if(!a)return;const r=new s.DocumentDropEdit(a.insertText);return r.yieldTo=[s.DocumentDropOrPasteEditKind.Text],r.additionalEdit=a.additionalEdit,r.title=s.l10n.t("Insert Image as Attachment"),r}async createInsertImageAttachmentEdit(t,e,o){const n=await async function(t,e){const o=m(await Promise.all(Array.from(t,(async([t,e])=>{if(!l.has(t))return;const o=e.asFile();if(!o)return;const n=await o.data();return{fileName:o.name,mimeType:t,data:n}}))));if(o.length)return o;const n=await(t.get("text/uri-list")?.asString());if(e.isCancellationRequested)return[];if(n){const t=[];for(const e of n.split(/\r?\n/g))try{t.push(s.Uri.parse(e))}catch{}return m(await Promise.all(t.map((async t=>{const e=function(t){return f.get((0,u.extname)(t.fsPath).toLowerCase())}(t);if(!e)return;const o=await s.workspace.fs.readFile(t);return{fileName:(0,u.basename)(t.fsPath),mimeType:e,data:o}}))))}return[]}(e,o);if(!n.length||o.isCancellationRequested)return;const a=function(t){for(const e of s.workspace.notebookDocuments)if(e.uri.path===t.uri.path)for(const o of e.getCells())if(o.document===t)return o}(t);if(!a)return;const r=function(t,e){const o={...t.metadata},n=[];if(e.length){o.attachments||(o.attachments={});for(const t of e){const e=p(t.data),a=(0,u.extname)(t.fileName),r=(0,u.basename)(t.fileName,a);let i=r+a;for(let n=2;i in o.attachments;n++){const s=Object.entries(o.attachments[i]);if(s.length){const[o,c]=s[0];if(o===t.mimeType&&c===e)break;i=r.concat(`-${n}`)+a}}n.push(i),o.attachments[i]={[t.mimeType]:e}}return{metadata:o,filenames:n}}}(a,n);if(!r)return;const i=new s.WorkspaceEdit,c=s.NotebookEdit.updateCellMetadata(a.index,r.metadata),d=a.notebook.uri;i.set(d,[c]);const h=new s.SnippetString;return r.filenames.forEach(((t,e)=>{h.appendText("!["),h.appendPlaceholder(`${t}`),h.appendText(`](${/\s/.test(t)?`<attachment:${t}>`:`attachment:${t}`})`),e!==r.filenames.length-1&&h.appendText(" ")})),{insertText:h,additionalEdit:i}}}function m(t){return t.filter((t=>!!t))}function p(t,e=!0,o=!1){const n=o?"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_":"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";let a="";const r=t.byteLength%3;let i=0;for(;i<t.byteLength-r;i+=3){const e=t[i+0],o=t[i+1],r=t[i+2];a+=n[e>>>2],a+=n[63&(e<<4|o>>>4)],a+=n[63&(o<<2|r>>>6)],a+=n[63&r]}if(1===r){const o=t[i+0];a+=n[o>>>2],a+=n[o<<4&63],e&&(a+="==")}else if(2===r){const o=t[i+0],r=t[i+1];a+=n[o>>>2],a+=n[63&(o<<4|r>>>4)],a+=n[r<<2&63],e&&(a+="=")}return a}h.kind=s.DocumentDropOrPasteEditKind.Empty.append("markdown","link","image","attachment")},269:(t,e,o)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.pendingNotebookCellModelUpdates=void 0,e.activate=function(t){n.workspace.onDidChangeNotebookDocument(h,void 0,t.subscriptions),n.workspace.onWillSaveNotebookDocument(l,void 0,t.subscriptions)},e.debounceOnDidChangeNotebookDocument=function(){const t=n.workspace.onDidChangeNotebookDocument((t=>{d(t.notebook)&&(s?s.notebook===t.notebook?s={cellChanges:t.cellChanges.concat(s.cellChanges),contentChanges:t.contentChanges.concat(s.contentChanges),notebook:t.notebook}:(u(),s=t):s=t,c&&clearTimeout(c),c=setTimeout(u,200))}));return n.Disposable.from(t,new n.Disposable((()=>{clearTimeout(c)})))};const n=o(398),a=o(624),r=o(829),i=()=>{};let s,c;function u(){if(c&&clearTimeout(c),!s)return;const t=s;s=void 0,h(t)}function d(t){return"jupyter-notebook"===t.notebookType}function l(t){if(!d(t.notebook))return;u();const o=e.pendingNotebookCellModelUpdates.get(t.notebook);o&&t.waitUntil(Promise.all(o))}e.pendingNotebookCellModelUpdates=new WeakMap;const f=new WeakSet;function h(t){if(!d(t.notebook))return;const o=t.notebook,s=(0,a.getNotebookMetadata)(t.notebook),c=s.metadata?.language_info?.name,u=[];t.cellChanges.forEach((t=>{if(!c||t.cell.kind!==n.NotebookCellKind.Code)return;const e=t.metadata?(0,a.getCellMetadata)({metadata:t.metadata}):(0,a.getCellMetadata)({cell:t.cell}),o=(0,a.getVSCodeCellLanguageId)(e),r=JSON.parse(JSON.stringify(e));r.metadata=r.metadata||{};let i=!1;t.executionSummary?.executionOrder&&"boolean"==typeof t.executionSummary.success&&e.execution_count!==t.executionSummary?.executionOrder?(r.execution_count=t.executionSummary.executionOrder,i=!0):t.executionSummary||t.metadata||0!==t.outputs?.length||!e.execution_count?t.executionSummary&&(t.executionSummary?.executionOrder||t.executionSummary?.success||t.executionSummary?.timing)||t.metadata||t.outputs||!e.execution_count||!f.has(t.cell)?t.executionSummary?.executionOrder||t.executionSummary?.success||t.executionSummary?.timing||t.metadata||t.outputs||!e.execution_count||f.has(t.cell)||(r.execution_count=null,i=!0):(r.execution_count=null,i=!0,f.delete(t.cell)):(r.execution_count=null,i=!0,f.add(t.cell)),t.document?.languageId&&t.document?.languageId!==c&&t.document?.languageId!==o?((0,a.setVSCodeCellLanguageId)(r,t.document.languageId),i=!0):(t.document?.languageId&&t.document.languageId===c&&o||t.document?.languageId&&t.document.languageId===c&&t.document.languageId===o)&&((0,a.removeVSCodeCellLanguageId)(r),i=!0),i&&u.push({cell:t.cell,metadata:r})})),t.contentChanges.forEach((e=>{e.addedCells.forEach((e=>{const o=(0,a.getCellMetadata)({cell:e});if(o.metadata){if(!m(s))return;if(m(s)&&o?.id)return}const n={...JSON.parse(JSON.stringify(o||{}))};n.metadata=n.metadata||{},m(s)&&!o?.id&&(n.id=function(t){for(;;){const e=(0,r.generateUuid)().replace(/-/g,"").substring(0,8);let o=!1;for(let n=0;n<t.cellCount;n++){const r=t.cellAt(n),i=(0,a.getCellMetadata)({cell:r})?.id;if(i&&i===e){o=!0;break}}if(!o)return e}}(t.notebook)),u.push({cell:e,metadata:n})}))})),u.length&&function(t,o){const r=e.pendingNotebookCellModelUpdates.get(t)??new Set;e.pendingNotebookCellModelUpdates.set(t,r);const s=new n.WorkspaceEdit;o.forEach((({cell:t,metadata:e})=>{const o={...t.metadata,...e};!e.execution_count&&o.execution_count&&(o.execution_count=null),!e.attachments&&o.attachments&&delete o.attachments,s.set(t.notebook.uri,[n.NotebookEdit.updateCellMetadata(t.index,(0,a.sortObjectPropertiesRecursively)(o))])}));const c=n.workspace.applyEdit(s).then(i,i);r.add(c);const u=()=>function(t,o){const n=e.pendingNotebookCellModelUpdates.get(t);n&&(n.delete(o),n.size||e.pendingNotebookCellModelUpdates.delete(t))}(t,c);c.then(u,u)}(o,u)}function m(t){return(t.nbformat||0)>=5||4===(t.nbformat||0)&&(t.nbformat_minor||0)>=5}},292:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.NotebookSerializer=void 0;const s=i(o(398)),c=o(829),u=o(208);class d extends u.NotebookSerializerBase{constructor(t){super(t),this.experimentalSave=s.workspace.getConfiguration("ipynb").get("experimental.serialization",!1),this.tasks=new Map,t.subscriptions.push(s.workspace.onDidChangeConfiguration((t=>{t.affectsConfiguration("ipynb.experimental.serialization")&&(this.experimentalSave=s.workspace.getConfiguration("ipynb").get("experimental.serialization",!1))})))}dispose(){try{this.worker?.terminate()}catch{}super.dispose()}async serializeNotebook(t,e){return this.disposed?new Uint8Array(0):this.experimentalSave?this.serializeViaWorker(t):super.serializeNotebook(t,e)}async startWorker(){if(this.disposed)throw new Error("Serializer disposed");if(this.worker)return this.worker;const{Worker:t}=await Promise.resolve().then((()=>i(o(919)))),e=-1!==this.context.extension.packageJSON.main.indexOf("/dist/")?"dist":"out";return this.worker=new t(s.Uri.joinPath(this.context.extensionUri,e,"notebookSerializerWorker.js").fsPath,{}),this.worker.on("exit",(t=>{this.disposed||console.error("IPynb Notebook Serializer Worker exited unexpectedly",t),this.worker=void 0})),this.worker.on("message",(t=>{const e=this.tasks.get(t.id);e&&(e.complete(t.data),this.tasks.delete(t.id))})),this.worker.on("error",(t=>{this.disposed||console.error("IPynb Notebook Serializer Worker errored unexpectedly",t)})),this.worker}async serializeViaWorker(t){const e=await this.startWorker(),o=(0,c.generateUuid)(),n=new c.DeferredPromise;return this.tasks.set(o,n),e.postMessage({data:t,id:o}),n.p}}e.NotebookSerializer=d},208:function(t,e,o){"use strict";var n,a=this&&this.__createBinding||(Object.create?function(t,e,o,n){void 0===n&&(n=o);var a=Object.getOwnPropertyDescriptor(e,o);a&&!("get"in a?!e.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return e[o]}}),Object.defineProperty(t,n,a)}:function(t,e,o,n){void 0===n&&(n=o),t[n]=e[o]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),i=this&&this.__importStar||(n=function(t){return n=Object.getOwnPropertyNames||function(t){var e=[];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[e.length]=o);return e},n(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var o=n(t),i=0;i<o.length;i++)"default"!==o[i]&&a(e,t,o[i]);return r(e,t),e}),s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.NotebookSerializerBase=void 0;const c=s(o(855)),u=i(o(398)),d=o(929),l=i(o(583)),f=o(624);class h extends u.Disposable{constructor(t){super((()=>{})),this.context=t,this.disposed=!1}dispose(){this.disposed=!0,super.dispose()}async deserializeNotebook(t,e){let o="";try{o=(new TextDecoder).decode(t)}catch{}let n=o&&/\S/.test(o)?JSON.parse(o):{};if(n.__webview_backup){const t=n.__webview_backup,e=this.context.globalStorageUri.with({path:this.context.globalStorageUri.path.replace("vscode.ipynb","ms-toolsai.jupyter")}),a=`${l.fast1a32hex(t)}.ipynb`,r=u.Uri.joinPath(e,a),i=await u.workspace.fs.readFile(r);n=i?JSON.parse(i.toString()):{},n.contents&&"string"==typeof n.contents&&(o=n.contents,n=JSON.parse(o))}if(n.nbformat&&n.nbformat<4)throw new Error("Only Jupyter notebooks version 4+ are supported");const a=o?(0,c.default)(o.substring(0,1e3)).indent:" ",r=(0,d.getPreferredLanguage)(n.metadata);0===(n.cells||[]).length&&(n.cells=[]),n.metadata&&(n.metadata.kernelspec||n.metadata.language_info)||(n.metadata=n.metadata||{},n.metadata.language_info=n.metadata.language_info||{name:r});const i=(0,d.jupyterNotebookModelToNotebookData)(n,r);return i.metadata=i.metadata||{},i.metadata.indentAmount=a,i}async serializeNotebook(t,e){if(this.disposed)return new Uint8Array(0);const o=(0,f.serializeNotebookToString)(t);return(new TextEncoder).encode(o)}}e.NotebookSerializerBase=h},624:(t,e,o)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createJupyterCellFromNotebookCell=r,e.sortObjectPropertiesRecursively=i,e.getCellMetadata=s,e.getVSCodeCellLanguageId=function(t){return t.metadata?.vscode?.languageId},e.setVSCodeCellLanguageId=c,e.removeVSCodeCellLanguageId=u,e.createMarkdownCellFromNotebookCell=p,e.pruneCell=g,e.serializeNotebookToString=function(t){const e=y(t),o=e.metadata?.language_info?.name??t.cells.find((t=>2===t.kind))?.languageId;e.cells=t.cells.map((t=>r(t,o))).map(g);return function(t,e){const o=i(t);return JSON.stringify(o,void 0,e)+"\n"}(e,t.metadata&&"indentAmount"in t.metadata&&"string"==typeof t.metadata.indentAmount?t.metadata.indentAmount:" ")},e.getNotebookMetadata=y;const n=o(712),a=new TextDecoder;function r(t,e){let o;return o=t.kind===n.NotebookCellKindMarkup?p(t):"raw"===t.languageId?function(t){const e=s({cell:t}),o={cell_type:"raw",source:d(t.value.replace(/\r\n/g,"\n")),metadata:e?.metadata||{}};return e?.attachments&&(o.attachments=e.attachments),e?.id&&(o.id=e.id),o}(t):function(t,e){const o=JSON.parse(JSON.stringify(s({cell:t})));o.metadata=o.metadata||{},t.languageId!==e?c(o,t.languageId):u(o);const n={cell_type:"code",execution_count:o.execution_count??null,source:d(t.value.replace(/\r\n/g,"\n")),outputs:(t.outputs||[]).map(l),metadata:o.metadata};return o?.id&&(n.id=o.id),n}(t,e),o}function i(t){return Array.isArray(t)?t.map(i):null!=t&&"object"==typeof t&&Object.keys(t).length>0?Object.keys(t).sort().reduce(((e,o)=>(e[o]=i(t[o]),e)),{}):t}function s(t){if("cell"in t){const e=t.cell,o={execution_count:null,...e.metadata??{}};return e.kind===n.NotebookCellKindMarkup&&delete o.execution_count,o}return{...t.metadata??{}}}function c(t,e){t.metadata=t.metadata||{},t.metadata.vscode={languageId:e}}function u(t){t.metadata?.vscode&&delete t.metadata.vscode}function d(t){if(Array.isArray(t))return t;const e=t.toString();if(e.length>0){const t=e.split("\n");return t.map(((e,o)=>o<t.length-1?`${e}\n`:e)).filter((t=>t.length>0))}return[]}function l(t){const e=t.metadata;let o;const a=e?.outputType;switch(a){case"error":o=f(t);break;case"stream":o=h(t);break;case"display_data":o={output_type:"display_data",data:t.items.reduce(((t,e)=>(t[e.mime]=m(e.mime,e.data),t)),{}),metadata:e?.metadata||{}};break;case"execute_result":o={output_type:"execute_result",data:t.items.reduce(((t,e)=>(t[e.mime]=m(e.mime,e.data),t)),{}),metadata:e?.metadata||{},execution_count:"number"==typeof e?.executionCount?e?.executionCount:null};break;case"update_display_data":o={output_type:"update_display_data",data:t.items.reduce(((t,e)=>(t[e.mime]=m(e.mime,e.data),t)),{}),metadata:e?.metadata||{}};break;default:{const a=1===t.items.length&&t.items.every((t=>t.mime===n.CellOutputMimeTypes.error)),r=t.items.every((t=>t.mime===n.CellOutputMimeTypes.stderr||t.mime===n.CellOutputMimeTypes.stdout));if(a)return f(t);const i=e?.outputType||(r?"stream":"display_data");let s;s="stream"===i?h(t):"display_data"===i?{data:{},metadata:{},output_type:"display_data"}:{output_type:i},e?.metadata&&(s.metadata=e.metadata),t.items.length>0&&(s.data=t.items.reduce(((t,e)=>(t[e.mime]=m(e.mime,e.data),t)),{})),o=s;break}}return o&&e&&e.transient&&(o.transient=e.transient),o}function f(t){const e=t.items[0];if(!e.data)return{output_type:"error",ename:"",evalue:"",traceback:[]};const o=t.metadata?.originalError,n=JSON.parse(a.decode(e.data));return{output_type:"error",ename:n.name,evalue:n.message,traceback:o?.traceback||d(n.stack||n.message||"")}}function h(t){const e=[];t.items.filter((t=>t.mime===n.CellOutputMimeTypes.stderr||t.mime===n.CellOutputMimeTypes.stdout)).map((t=>a.decode(t.data))).forEach((t=>{const o=t.split("\n");e.length&&o.length&&o[0].length>0&&(e[e.length-1]=`${e[e.length-1]}${o.shift()}`);for(const t of o)e.push(t)}));for(let t=0;t<e.length-1;t++)e[t]=`${e[t]}\n`;e.length&&0===e[e.length-1].length&&e.pop();const o=function(t){if(t.items.length>0)return t.items[0].mime===n.CellOutputMimeTypes.stderr?"stderr":"stdout"}(t)||"stdout";return{output_type:"stream",name:o,text:e}}function m(t,e){if(!e)return"";try{if(t===n.CellOutputMimeTypes.error){const t=a.decode(e);return JSON.parse(t)}if(t.startsWith("text/")||n.textMimeTypes.includes(t))return d(a.decode(e));if(t.startsWith("image/")&&"image/svg+xml"!==t)return"undefined"!=typeof Buffer&&"function"==typeof Buffer.from?Buffer.from(e).toString("base64"):btoa(e.reduce(((t,e)=>t+String.fromCharCode(e)),""));if(t.toLowerCase().includes("json")){const t=a.decode(e);return t.length>0?JSON.parse(t):t}return"image/svg+xml"===t?d(a.decode(e)):a.decode(e)}catch(t){return""}}function p(t){const e=s({cell:t}),o={cell_type:"markdown",source:d(t.value.replace(/\r\n/g,"\n")),metadata:e?.metadata||{}};return e?.attachments&&(o.attachments=e.attachments),e?.id&&(o.id=e.id),o}function g(t){const e={...t,source:d(t.source)};return"code"!==e.cell_type?(delete e.outputs,delete e.execution_count):e.outputs=e.outputs?e.outputs.map(C):[],e}const b={stream:new Set(Object.keys({output_type:"stream",name:"stdout",text:""})),error:new Set(Object.keys({output_type:"error",ename:"",evalue:"",traceback:[""]})),display_data:new Set(Object.keys({output_type:"display_data",data:{},metadata:{}})),execute_result:new Set(Object.keys({output_type:"execute_result",name:"",execution_count:0,data:{},metadata:{}}))};function C(t){let e;switch(t.output_type){case"stream":case"error":case"execute_result":case"display_data":e=b[t.output_type];break;default:return t}const o={...t};for(const n of Object.keys(t))e.has(n)||delete o[n];return o}function y(t){const e=t.metadata||{},o={};return o.cells=e.cells||[],o.nbformat=e.nbformat||n.defaultNotebookFormat.major,o.nbformat_minor=e.nbformat_minor??n.defaultNotebookFormat.minor,o.metadata=e.metadata||{},o}},398:t=>{"use strict";t.exports=require("vscode")},919:t=>{"use strict";t.exports=require("node:worker_threads")},928:t=>{"use strict";t.exports=require("path")}},e={},o=function o(n){var a=e[n];if(void 0!==a)return a.exports;var r=e[n]={exports:{}};return t[n].call(r.exports,r,r.exports,o),r.exports}(772),n=exports;for(var a in o)n[a]=o[a];o.__esModule&&Object.defineProperty(n,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6cb10202a0877c8b8d36fff300b7791e33a4d3e7/extensions/ipynb/dist/ipynbMain.node.js.map