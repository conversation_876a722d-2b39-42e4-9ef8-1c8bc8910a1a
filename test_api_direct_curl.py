#!/usr/bin/env python3
"""
Prueba directa de API key usando requests HTTP
"""
import requests
import json
import sys

def test_api_key_direct(api_key):
    """Prueba directa de la API key usando HTTP requests"""
    
    print(f"🔍 Probando API key: {api_key[:20]}...")
    print(f"📏 Longitud: {len(api_key)} caracteres")
    
    # URL de la API de Gemini
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    # Headers
    headers = {
        'Content-Type': 'application/json'
    }
    
    # Payload de prueba
    data = {
        "contents": [{
            "parts": [{"text": "Hola, responde solo 'OK' si funciona"}]
        }]
    }
    
    try:
        print("🌐 Enviando solicitud HTTP...")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ ¡API KEY VÁLIDA! - Conexión exitosa")
            result = response.json()
            try:
                text_response = result['candidates'][0]['content']['parts'][0]['text']
                print(f"📝 Respuesta: {text_response}")
                return True
            except KeyError:
                print("⚠️ Respuesta recibida pero formato inesperado")
                print(f"📄 Respuesta completa: {json.dumps(result, indent=2)}")
                return True
                
        elif response.status_code == 400:
            print("❌ API KEY INVÁLIDA - Error 400")
            error_data = response.json()
            print(f"📄 Error: {json.dumps(error_data, indent=2)}")
            return False
            
        elif response.status_code == 403:
            print("❌ PERMISOS INSUFICIENTES - Error 403")
            print("🔧 Posibles soluciones:")
            print("   - Habilitar la API de Generative Language")
            print("   - Verificar permisos del proyecto")
            print("   - Asociar cuenta de facturación")
            error_data = response.json()
            print(f"📄 Error: {json.dumps(error_data, indent=2)}")
            return False
            
        else:
            print(f"❌ ERROR INESPERADO - Status {response.status_code}")
            print(f"📄 Respuesta: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ TIMEOUT - La solicitud tardó demasiado")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ ERROR DE CONEXIÓN - Verifica tu internet")
        return False
    except Exception as e:
        print(f"❌ ERROR INESPERADO: {e}")
        return False

def main():
    """Función principal"""
    print("🧪 PRUEBA DIRECTA DE API KEYS DE GEMINI")
    print("=" * 50)
    
    # Lista de API keys para probar
    api_keys = [
        "AIzaSyA_4RAebHhJKJyteo4vhygQkp4jPpbTt3A",  # Más reciente
        "AIzaSyAqTJGcOTaw8hXkWK7GVVbegLieB2FYHlY",  # Segunda
        "AizaSyAghQk7L3QaE7ZRMSfyMf5YUqjlsfCX0_k"   # Primera
    ]
    
    valid_keys = []
    
    for i, api_key in enumerate(api_keys, 1):
        print(f"\n🔑 PRUEBA {i}/3:")
        print("-" * 30)
        
        if test_api_key_direct(api_key):
            valid_keys.append(api_key)
            print(f"✅ API Key {i} es VÁLIDA")
        else:
            print(f"❌ API Key {i} es INVÁLIDA")
    
    print("\n" + "=" * 50)
    print("📊 RESUMEN FINAL:")
    
    if valid_keys:
        print(f"✅ {len(valid_keys)} API key(s) válida(s) encontrada(s)")
        for i, key in enumerate(valid_keys, 1):
            print(f"   {i}. {key[:20]}...")
    else:
        print("❌ NINGUNA API KEY ES VÁLIDA")
        print("\n🔧 ACCIONES REQUERIDAS:")
        print("1. Ve a https://aistudio.google.com/app/apikey")
        print("2. Crea una nueva API key en un proyecto nuevo")
        print("3. Ve a https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com")
        print("4. Habilita la API de Generative Language")
        print("5. Asocia una cuenta de facturación al proyecto")

if __name__ == "__main__":
    main()
