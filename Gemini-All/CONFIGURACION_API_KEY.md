# 🔐 Guía de Configuración de API Key para Gemini CLI

## 📋 Opciones de Autenticación

Gemini CLI ofrece dos métodos principales de autenticación:

### 1. 🔑 API Key de Google AI Studio (Recomendado para uso personal)
- **Límites**: 60 solicitudes/minuto, 1,000 solicitudes/día
- **Costo**: Gratuito
- **Configuración**: Más simple

### 2. 👤 Cuenta Personal de Google (OAuth)
- **Límites**: Similares a API Key
- **Costo**: Gratuito
- **Configuración**: Automática durante primera ejecución

## 🚀 Método 1: Configuración con API Key

### Paso 1: Obtener API Key
1. Ve a [Google AI Studio](https://aistudio.google.com/apikey)
2. Inicia sesión con tu cuenta de Google
3. Haz clic en "Create API Key"
4. Selecciona un proyecto existente o crea uno nuevo
5. Copia la API Key generada

### Paso 2: Configurar en el Entorno
```bash
# Activar el entorno Gemini-All
cd ~/Gemini-All
source activate-gemini-env.sh

# Crear archivo .env con tu API Key
cp .env.example .env
nano .env  # O usa tu editor preferido
```

### Paso 3: Editar .env
Reemplaza `your_api_key_here` con tu API Key real:
```bash
GEMINI_API_KEY=tu_api_key_aqui
```

### Paso 4: Verificar Configuración
```bash
# Recargar el entorno
source activate-gemini-env.sh

# Verificar que la variable esté configurada
echo $GEMINI_API_KEY
```

## 🔄 Método 2: Autenticación con Cuenta Google

### Configuración Automática
```bash
# Activar el entorno
cd ~/Gemini-All
source activate-gemini-env.sh

# Ejecutar Gemini CLI por primera vez
gemini

# Seguir las instrucciones en pantalla para autenticación
```

## ⚠️ Consideraciones de Seguridad

### Proteger tu API Key
- ✅ **NUNCA** compartas tu API Key públicamente
- ✅ **NUNCA** subas archivos .env a repositorios git
- ✅ Usa variables de entorno para la API Key
- ✅ Regenera la API Key si se compromete

### Archivo .gitignore
Si planeas usar git en este proyecto, asegúrate de tener:
```gitignore
.env
.env.local
.env.*.local
```

## 🧪 Prueba de Configuración

### Comando de Prueba Básico
```bash
# Activar entorno
source activate-gemini-env.sh

# Prueba simple
echo "Hola, ¿cómo estás?" | gemini
```

### Comando de Prueba con Prompt
```bash
gemini -p "Explica qué es la inteligencia artificial en una oración"
```

## 🔧 Solución de Problemas

### Error: "API Key not found"
- Verifica que GEMINI_API_KEY esté configurada: `echo $GEMINI_API_KEY`
- Recarga el entorno: `source activate-gemini-env.sh`
- Verifica que el archivo .env existe y tiene el formato correcto

### Error: "Authentication failed"
- Verifica que la API Key sea válida
- Comprueba que no haya espacios extra en la API Key
- Regenera la API Key en Google AI Studio si es necesario

### Error: "Rate limit exceeded"
- Espera un momento antes de hacer otra solicitud
- Considera usar autenticación OAuth para límites más altos

## 📚 Próximos Pasos

Una vez configurada la autenticación:
1. ✅ Realizar primera ejecución exitosa
2. ✅ Seleccionar tema de color preferido
3. ✅ Probar funcionalidades básicas
4. ✅ Explorar capacidades avanzadas

---
*Guía creada durante la instalación de Gemini CLI en Manjaro XFCE*
