# 🔧 Errores y Soluciones - Gemini Fullstack LangGraph Quickstart

## 📋 Información del Sistema
- **Sistema Operativo**: Manjaro XFCE
- **Python**: 3.13.3
- **Node.js**: Instalado via pacman
- **Docker**: 28.2.2
- **Docker Compose**: 2.37.1

---

## ❌ Errores Encontrados y ✅ Soluciones

### 1. **Error: pip no instalado**
**Descripción**: `bash: pip: command not found`

**Causa**: pip no viene instalado por defecto en Manjaro

**Solución**:
```bash
sudo pacman -S python-pip
```

---

### 2. **Error: python3-venv no disponible**
**Descripción**: `No module named venv`

**Causa**: El módulo venv no está disponible en la instalación base

**Solución**:
```bash
sudo pacman -S python-virtualenv
```

---

### 3. **Error: Docker no instalado**
**Descripción**: `docker: command not found`

**Causa**: Docker no está instalado en el sistema

**Solución**:
```bash
sudo pacman -S docker docker-compose
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker $USER
```

**Nota**: Después de agregar el usuario al grupo docker, es necesario cerrar sesión y volver a iniciar o usar `newgrp docker`.

---

### 4. **Error: Permisos de Docker**
**Descripción**: 
```
permission denied while trying to connect to the Docker daemon socket at unix:///var/run/docker.sock
```

**Causa**: El usuario no tiene permisos para acceder al socket de Docker

**Solución**:
```bash
sudo usermod -aG docker $USER
newgrp docker
# O alternativamente, usar sudo:
sudo docker build -t imagen .
```

---

### 5. **Error: Vulnerabilidad en dependencias npm**
**Descripción**: `1 low severity vulnerability` al instalar dependencias del frontend

**Causa**: Dependencias desactualizadas en el proyecto

**Solución**:
```bash
cd frontend
npm audit fix
```

**Resultado**: Vulnerabilidad corregida exitosamente.

---

### 6. **Error: LangGraph __version__ no disponible**
**Descripción**: `module 'langgraph' has no attribute '__version__'`

**Causa**: Algunas versiones de LangGraph no exponen el atributo __version__

**Solución**: 
- Verificar importación directa de módulos específicos
- No depender del atributo __version__ para validación

**Código de prueba funcional**:
```python
import langgraph
from langgraph.graph import StateGraph
from langgraph.prebuilt import ToolNode
# Todas las importaciones funcionan correctamente
```

---

### 7. **Error: Archivo .env en ubicación incorrecta**
**Descripción**: El archivo .env se guarda en el directorio raíz del workspace en lugar del directorio del proyecto

**Causa**: Configuración de rutas en las herramientas de edición

**Solución**:
```bash
mv /ruta/incorrecta/.env /ruta/correcta/.env
```

---

## 🎯 Configuraciones Exitosas

### ✅ Entorno Virtual Python
```bash
python3 -m venv Gemini-Total-Web
source Gemini-Total-Web/bin/activate
pip install --upgrade pip
```

### ✅ Variables de Entorno
Archivo `.env` en `/backend/`:
```env
GEMINI_API_KEY=tu_api_key_aqui
# Variables adicionales para producción
# LANGSMITH_API_KEY=
# REDIS_URI=redis://localhost:6379
# POSTGRES_URI=postgres://postgres:postgres@localhost:5432/postgres
```

### ✅ Instalación de Dependencias
**Backend**:
```bash
cd backend
pip install .
# 72 paquetes instalados exitosamente
```

**Frontend**:
```bash
cd frontend
npm install
npm audit fix
npm run build
# 390 paquetes instalados, vulnerabilidades corregidas
```

### ✅ Verificación de Funcionamiento
**Backend**:
```bash
source ../../Gemini-Total-Web/bin/activate
langgraph dev
# Servidor iniciado en http://127.0.0.1:2024
```

**Frontend**:
```bash
npm run dev
# Servidor iniciado en http://localhost:5173/app/
```

---

## 🚀 Comandos de Inicio Rápido

### Desarrollo Local
```bash
# Terminal 1 - Backend
cd backend
source ../../Gemini-Total-Web/bin/activate
langgraph dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

### Producción con Docker
```bash
# Construir imagen
sudo docker build -t gemini-fullstack-langgraph .

# Ejecutar con docker-compose
sudo docker-compose up -d
```

---

## 📊 Estado Final del Proyecto

### ✅ Componentes Funcionando
- [x] Entorno virtual Python configurado
- [x] Dependencias backend instaladas (72 paquetes)
- [x] Dependencias frontend instaladas (390 paquetes)
- [x] API Gemini conectada y funcional
- [x] Backend LangGraph ejecutándose
- [x] Frontend React ejecutándose
- [x] Aplicación web accesible

### ⚠️ Pendientes para Producción
- [ ] Configuración completa de Docker (permisos)
- [ ] Optimizaciones de seguridad
- [ ] Configuración de variables de entorno para producción
- [ ] Pruebas de carga y rendimiento

---

## 🔗 URLs de Acceso

- **Frontend**: http://localhost:5173/app/
- **Backend API**: http://127.0.0.1:2024
- **API Docs**: http://127.0.0.1:2024/docs
- **Studio UI**: https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024

---

## 📝 Notas Adicionales

1. **Tiempo de instalación total**: Aproximadamente 30-45 minutos
2. **Espacio en disco requerido**: ~2GB para dependencias
3. **Memoria RAM recomendada**: Mínimo 4GB, recomendado 8GB
4. **Conexión a internet**: Requerida para descargar dependencias y usar API Gemini

---

*Documento generado automáticamente durante la instalación del proyecto Gemini Fullstack LangGraph Quickstart en Manjaro XFCE*
